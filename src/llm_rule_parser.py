"""
llm_rule_parser.py

Parses LLM-generated trading rules into executable Python functions for backtesting
and generates MT4/MT5 Expert Advisor code.
Handles the actual LLM output format from the pattern discovery system.
"""
import re
from typing import Callable, List, Dict, Tuple, Optional
from dataclasses import dataclass
from datetime import timedelta

class RuleParseError(Exception):
    pass

@dataclass
class TradingRule:
    """Structured representation of a multi-dimensional trading rule"""
    rule_id: int
    entry_condition: str
    direction: str  # 'long' or 'short'
    stop_loss: str
    exit_condition: str
    time_filters: str
    position_size: Optional[str] = None  # LLM-provided position size (absolute units)
    profit_target: Optional[str] = None
    max_duration: Optional[str] = None
    # Multi-dimensional pattern fields
    primary_timeframe: str = "5min"  # Execution timeframe
    setup_timeframes: Optional[str] = None  # Setup context timeframes (comma-separated)
    market_situation: str = ""  # Multi-dimensional context description
    participant_behavior: str = ""  # Behavioral edge explanation
    cross_timeframe_setup: str = ""  # Timeframe relationship description

class LLMRuleParser:
    """
    Advanced parser for LLM-generated trading rules that can handle
    the actual output format and generate both Python and MT4/MT5 code
    """
    
    def __init__(self):
        self.rules = []
        self.validation_errors = []
    
    def parse_llm_response(self, llm_response: str) -> List[TradingRule]:
        """
        Parse the complete LLM response and extract structured trading rules
        """
        self.rules = []
        self.validation_errors = []
        
        # Extract the RULE CLARITY REQUIREMENT section
        rule_section = self._extract_rule_section(llm_response)
        if not rule_section:
            raise RuleParseError("No 'RULE CLARITY REQUIREMENT' section found in LLM response")
        
        # Parse individual rules
        individual_rules = self._extract_individual_rules(rule_section)
        
        for i, rule_text in enumerate(individual_rules, 1):
            try:
                rule = self._parse_single_rule(i, rule_text)
                if rule:
                    self.rules.append(rule)
            except Exception as e:
                self.validation_errors.append(f"Rule {i} parsing failed: {str(e)}")
        
        if not self.rules and self.validation_errors:
            raise RuleParseError(f"No valid rules parsed. Errors: {'; '.join(self.validation_errors)}")
        
        return self.rules
    
    def _extract_rule_section(self, text: str) -> str:
        """Extract the rule section with flexible formatting to match actual LLM output"""
        # The LLM generates multiple BREAKOUT EXECUTION RULE sections, so we need to extract all of them
        # Return the entire text since it contains all the rules we need
        return text
    
    def _extract_individual_rules(self, rule_section: str) -> List[str]:
        """Extract individual MT4 pattern sections"""
        # PRIMARY: Handle MT4 format with ### PATTERN X: or **PATTERN X:** markers
        mt4_pattern_rules = re.findall(
            r'(?:###\s*PATTERN \d+:|PATTERN \d+:|\*\*PATTERN \d+:).*?(?=(?:###\s*PATTERN|\*\*PATTERN|PATTERN \d+:)|\Z)',
            rule_section,
            re.DOTALL | re.IGNORECASE
        )

        if mt4_pattern_rules:
            # Filter out patterns that actually contain MT4 fields
            valid_mt4_rules = []
            for rule in mt4_pattern_rules:
                if 'MT4 Entry:' in rule and 'MT4 Direction:' in rule:
                    valid_mt4_rules.append(rule.strip())
                    print(f"✅ Found valid MT4 pattern: {rule[:100]}...")

            if valid_mt4_rules:
                return valid_mt4_rules

        # FALLBACK: Handle old LM Studio format with **PATTERN X:** markers
        pattern_rules = re.findall(
            r'\*\*PATTERN \d+:.*?\n\s*BREAKOUT EXECUTION RULE:\s*\n(.*?)(?=\n\s*\*\*PATTERN|\Z)',
            rule_section,
            re.DOTALL | re.IGNORECASE
        )

        if pattern_rules:
            return [rule.strip() for rule in pattern_rules if rule.strip()]

        # Fallback: Find all BREAKOUT EXECUTION RULE sections without pattern markers
        breakout_rules = re.findall(
            r'BREAKOUT EXECUTION RULE:\s*\n(.*?)(?=\n\s*BREAKOUT EXECUTION RULE:|\n\s*\*\*PATTERN|\Z)',
            rule_section,
            re.DOTALL | re.IGNORECASE
        )

        if breakout_rules:
            return [rule.strip() for rule in breakout_rules if rule.strip()]

        # FIXED: NO FALLBACKS - LLM must provide properly formatted rules
        raise RuleParseError("LLM must provide rules in recognized format (PATTERN X: or BREAKOUT EXECUTION RULE:)")
    
    def _parse_single_rule(self, rule_id: int, rule_text: str) -> Optional[TradingRule]:
        """Parse a single rule into structured format with multi-dimensional support"""

        # Extract MT4-ready components using regex patterns
        # CRITICAL FIX: Handle multiple MT4 Entry lines (LLM sometimes generates time + price conditions)
        entry_condition = self._extract_price_entry_condition(rule_text)
        direction = self._extract_field(rule_text, r'MT4 Direction:\s*([^\n]+)')
        stop_loss = self._extract_field(rule_text, r'MT4 Stop:\s*([^\n]+)')
        exit_condition = self._extract_field(rule_text, r'MT4 Target:\s*([^\n]+)')
        position_size = self._extract_field(rule_text, r'MT4 Position Size:\s*([^\n]+)')

        # Extract optimal times as informational setting (not restrictive filter)
        optimal_times = self._extract_field(rule_text, r'Optimal Times:\s*([^\n]+)')

        # NO TIME FILTERS - All patterns execute 24/7
        time_filters = ""

        # Fallback to old format if MT4 format not found
        if not entry_condition:
            entry_condition = self._extract_field(rule_text, r'Entry condition:\s*([^\n]+)')
        if not direction:
            direction = self._extract_field(rule_text, r'Direction:\s*([^\n]+)')
        if not stop_loss:
            stop_loss = self._extract_field(rule_text, r'Stop loss:\s*([^\n]+)')
        if not exit_condition:
            exit_condition = self._extract_field(rule_text, r'Exit condition:\s*([^\n]+)')
        if not time_filters:
            time_filters = self._extract_field(rule_text, r'Time filters:\s*([^\n]+)')

        # Extract multi-dimensional pattern fields
        primary_timeframe = self._extract_field(rule_text, r'Primary timeframe:\s*([^\n]+)')
        setup_timeframes = self._extract_field(rule_text, r'Setup timeframes:\s*([^\n]+)')
        market_situation = self._extract_field(rule_text, r'Market Situation:\s*([^\n]+)')
        participant_behavior = self._extract_field(rule_text, r'Participant Behavior:\s*([^\n]+)')
        cross_timeframe_setup = self._extract_field(rule_text, r'Cross-Timeframe Setup:\s*([^\n]+)')

        # Validate required fields
        if not all([entry_condition, direction, stop_loss, exit_condition]):
            return None

        # FIXED: Convert MT4 direction format to our format with better parsing
        direction_clean = direction.strip().upper()
        if direction_clean == "OP_BUY" or "BUY" in direction_clean:
            direction = "long"
        elif direction_clean == "OP_SELL" or "SELL" in direction_clean:
            direction = "short"
        elif "LONG" in direction_clean:
            direction = "long"
        elif "SHORT" in direction_clean:
            direction = "short"
        else:
            direction = direction.lower().strip()

        return TradingRule(
            rule_id=rule_id,
            entry_condition=entry_condition,
            direction=direction,
            stop_loss=stop_loss,
            exit_condition=exit_condition,
            time_filters=time_filters or "",
            position_size=position_size,
            primary_timeframe=primary_timeframe or "5min",
            setup_timeframes=setup_timeframes,
            market_situation=market_situation,
            participant_behavior=participant_behavior,
            cross_timeframe_setup=cross_timeframe_setup
        )
    
    def _extract_field(self, text: str, pattern: str) -> str:
        """Extract a field using regex pattern with improved parsing"""
        match = re.search(pattern, text, re.IGNORECASE)
        if match:
            result = match.group(1).strip()
            # FIXED: Clean up bullet points and extra formatting more carefully
            result = re.sub(r'^\*\s+', '', result)  # Remove leading bullet points with space
            result = re.sub(r'^\*', '', result)     # Remove standalone asterisk
            result = result.strip()  # Clean up any remaining whitespace
            return result
        return ""

    def _extract_price_entry_condition(self, text: str) -> str:
        """Extract price entry condition, handling multiple MT4 Entry lines"""
        # Find all MT4 Entry lines
        entry_matches = re.findall(r'MT4 Entry:\s*([^\n]+)', text, re.IGNORECASE)

        if not entry_matches:
            return ""

        # If only one entry, return it
        if len(entry_matches) == 1:
            return entry_matches[0].strip()

        # If multiple entries, find the one that contains price conditions (not time filters)
        price_conditions = []
        time_conditions = []

        for entry in entry_matches:
            entry_clean = entry.strip().lower()
            # Check if it's a time condition
            if ('hour()' in entry_clean or 'time' in entry_clean or
                ('hour' in entry_clean and '>=' in entry_clean and '<=' in entry_clean)):
                time_conditions.append(entry.strip())
            # Check if it's a price condition
            elif any(price_term in entry_clean for price_term in
                    ['Close[', 'High[', 'Low[', 'Open[', '>', '<', 'break']):
                price_conditions.append(entry.strip())

        # Return the first price condition found
        if price_conditions:
            return price_conditions[0]

        # If no price conditions found, return the first entry
        return entry_matches[0].strip()



    def generate_python_functions(self) -> List[Callable]:
        """Generate Python functions for backtesting"""
        functions = []
        
        for rule in self.rules:
            func = self._create_python_function(rule)
            if func:
                functions.append(func)
        
        return functions
    
    def _create_python_function(self, rule: TradingRule) -> Optional[Callable]:
        """Create a Python function from a trading rule"""

        # Capture rule values to avoid closure issues
        rule_entry_condition = rule.entry_condition
        rule_direction = rule.direction
        rule_stop_loss = rule.stop_loss
        rule_exit_condition = rule.exit_condition
        rule_time_filters = rule.time_filters
        rule_position_size = rule.position_size
        rule_id = rule.rule_id

        def rule_function(data, current_idx):
            """Generated trading rule function"""
            # CRITICAL FIX: Handle both calling conventions
            # 1. Debug script: rule_function(history_up_to_current, current_idx)
            # 2. Backtester: rule_function(full_data, current_idx)

            if isinstance(current_idx, int):
                # Backtester calling convention: data is full dataset, current_idx is index
                if current_idx < 1 or current_idx >= len(data):
                    return None
                current = data.iloc[current_idx]
                previous = data.iloc[current_idx - 1]
                history = data.iloc[:current_idx + 1]  # History up to and including current
            else:
                # Debug script calling convention: data is history, current_idx might be ignored
                if len(data) < 2:
                    return None
                current = data.iloc[-1]
                previous = data.iloc[-2]
                history = data

            # Parse entry condition using captured values
            entry_signal = self._evaluate_entry_condition(rule_entry_condition, current, previous, history)
            if not entry_signal:
                return None

            # Check time filters using captured values
            if rule_time_filters and not self._check_time_filter(rule_time_filters, current):
                return None

            # Calculate entry price, stop loss, and take profit using captured values
            entry_price = current['Close']
            stop_loss_price = self._calculate_stop_loss(rule_stop_loss, entry_price, current, previous, rule_direction)
            take_profit_price = self._calculate_take_profit(rule_exit_condition, entry_price, stop_loss_price, rule_direction)

            if stop_loss_price is None:
                return None

            # Extract position size from LLM rule (absolute units) and convert to backtester format
            llm_position_size = self._parse_position_size(rule_position_size) if rule_position_size else 1

            # CRITICAL FIX: Use proper lot sizes for backtesting library
            # The backtesting library expects lot sizes similar to MT4 (0.01, 0.1, 1.0)
            # NOT fractional capital percentages

            # Convert LLM units to standard lot sizes
            # Standard forex lot sizes: 0.01 (micro), 0.1 (mini), 1.0 (standard)
            if float(llm_position_size) <= 0.1:
                position_size = 0.01  # Micro lot
            elif float(llm_position_size) <= 1.0:
                position_size = 0.1   # Mini lot
            else:
                position_size = 1.0   # Standard lot

            # DEBUG: Log position size calculation for first few signals
            if current_idx < 10:
                print(f"🔢 Position Size Calculation:")
                print(f"   LLM units: {llm_position_size}")
                print(f"   Entry price: {entry_price:.1f}")
                print(f"   Lot size: {position_size} (standard trading lot)")

            return {
                'direction': rule_direction,
                'entry_price': entry_price,
                'stop_loss': stop_loss_price,
                'take_profit': take_profit_price,
                'position_size': position_size,
                'rule_id': rule_id,
                'max_minutes': self._extract_max_duration(rule_exit_condition)
            }

        return rule_function

    def _parse_position_size(self, position_size_str: str) -> int:
        """Parse position size from LLM string to absolute units"""
        if not position_size_str:
            return 1

        # Extract numeric value from position size string
        # Handle formats like "5 units", "10", "3 lots", etc.
        import re
        numeric_match = re.search(r'(\d+(?:\.\d+)?)', position_size_str)
        if numeric_match:
            return int(float(numeric_match.group(1)))

        return 1  # Default fallback

    def _evaluate_entry_condition(self, condition: str, current, previous, history) -> bool:
        """Evaluate comprehensive entry condition logic with cross-timeframe support"""
        # CRITICAL FIX: Keep original case for MT4 syntax, only lowercase for text comparisons
        condition_original = condition
        condition_lower = condition.lower()

        # Handle complex cross-timeframe conditions like:
        # "Close > previous 5-minute high when 15-minute candle is bullish"
        if " when " in condition_lower:
            main_condition, cross_tf_condition = condition_lower.split(" when ", 1)

            # Evaluate main condition - pass original case for MT4 syntax
            main_result = self._evaluate_simple_condition(main_condition.strip(), current, previous, history, condition_original)
            if not main_result:
                return False

            # Evaluate cross-timeframe condition
            cross_tf_result = self._evaluate_cross_timeframe_condition(cross_tf_condition.strip(), current, history)
            return cross_tf_result

        # Handle simple conditions without cross-timeframe requirements - pass original case
        return self._evaluate_simple_condition(condition_lower, current, previous, history, condition_original)

    def _evaluate_simple_condition(self, condition: str, current, previous, history, condition_original: str = None) -> bool:
        """Evaluate simple entry conditions"""
        # Use original case for MT4 syntax if provided
        if condition_original is None:
            condition_original = condition
        # CRITICAL FIX: Handle "Close > previous 1-hour high" format
        if "close > previous" in condition and ("minute high" in condition or "hour high" in condition):
            # Handle hour format first (1-hour, 2-hour, etc.)
            hour_match = re.search(r'(\d+)-hour', condition)
            if hour_match:
                hours = int(hour_match.group(1))
                minutes = hours * 60  # Convert hours to minutes
                return self._check_timeframe_breakout(history, current, minutes, 'high', '>')

            # Handle minute format (5-minute, 15-minute, etc.)
            minute_match = re.search(r'(\d+)-minute', condition)
            if minute_match:
                minutes = int(minute_match.group(1))
                return self._check_timeframe_breakout(history, current, minutes, 'high', '>')

            # Fallback to previous bar high
            return current['Close'] > previous['High']

        # CRITICAL FIX: Handle "Close < previous X-hour/minute low"
        elif "close < previous" in condition and ("minute low" in condition or "hour low" in condition):
            # Handle hour format first
            hour_match = re.search(r'(\d+)-hour', condition)
            if hour_match:
                hours = int(hour_match.group(1))
                minutes = hours * 60  # Convert hours to minutes
                return self._check_timeframe_breakout(history, current, minutes, 'low', '<')

            # Handle minute format
            minute_match = re.search(r'(\d+)-minute', condition)
            if minute_match:
                minutes = int(minute_match.group(1))
                return self._check_timeframe_breakout(history, current, minutes, 'low', '<')

            return current['Close'] < previous['Low']

        # Handle "close > previous day's high by X%"
        elif "previous day's high" in condition:
            pct_match = re.search(r'(\d+\.?\d*)%', condition)
            if not pct_match:
                raise RuleParseError(f"LLM must specify percentage for 'previous day's high' condition. Got: '{condition}'")
            threshold_pct = float(pct_match.group(1)) / 100

            # Get previous day's high (look back at least 1440 minutes = 1 day)
            prev_day_high = self._get_previous_day_high(history)
            if prev_day_high is None:
                return False

            return current['Close'] > prev_day_high * (1 + threshold_pct)

        # Handle "close < previous day's low by X%"
        elif "previous day's low" in condition:
            pct_match = re.search(r'(\d+\.?\d*)%', condition)
            if not pct_match:
                raise RuleParseError(f"LLM must specify percentage for 'previous day's low' condition. Got: '{condition}'")
            threshold_pct = float(pct_match.group(1)) / 100

            # Get previous day's low
            prev_day_low = self._get_previous_day_low(history)
            if prev_day_low is None:
                return False

            return current['Close'] < prev_day_low * (1 - threshold_pct)

        # Handle "high > previous high by X%" (previous bar)
        elif "high > previous high" in condition:
            pct_match = re.search(r'(\d+\.?\d*)%', condition)
            if not pct_match:
                # Allow simple "high > previous high" without percentage
                threshold_pct = 0.0
            else:
                threshold_pct = float(pct_match.group(1)) / 100
            prev_high = previous['High']
            return current['High'] > prev_high * (1 + threshold_pct)

        # Handle "close > previous close" (simple momentum)
        elif "close > previous close" in condition:
            return current['Close'] > previous['Close']

        # Handle "close < previous close" (simple momentum)
        elif "close < previous close" in condition:
            return current['Close'] < previous['Close']

        # CRITICAL FIX: Handle MT4 syntax with comprehensive parsing
        # Check if this is an MT4 condition (contains [0] or [1] syntax)
        elif self._is_mt4_condition(condition_original):
            return self._evaluate_mt4_condition(condition_original, current, previous, history)

        # CRITICAL FIX: Handle MT4 negative indexing syntax like "High[0] > Close[-1]"
        elif "High[0] > Close[-1]" in condition:
            print(f"🎯 MT4 Logic: High[0] > Close[-1] -> {current['High']} > {previous['Close']} = {current['High'] > previous['Close']}")
            return current['High'] > previous['Close']

        elif "Low[0] < Close[-1]" in condition:
            print(f"🎯 MT4 Logic: Low[0] < Close[-1] -> {current['Low']} < {previous['Close']} = {current['Low'] < previous['Close']}")
            return current['Low'] < previous['Close']

        elif "High[0] > Low[-1]" in condition:
            print(f"🎯 MT4 Logic: High[0] > Low[-1] -> {current['High']} > {previous['Low']} = {current['High'] > previous['Low']}")
            return current['High'] > previous['Low']

        elif "Close[0] > Close[-1]" in condition:
            print(f"🎯 MT4 Logic: Close[0] > Close[-1] -> {current['Close']} > {previous['Close']} = {current['Close'] > previous['Close']}")
            return current['Close'] > previous['Close']

        elif "Close[0] < Close[-1]" in condition:
            print(f"🎯 MT4 Logic: Close[0] < Close[-1] -> {current['Close']} < {previous['Close']} = {current['Close'] < previous['Close']}")
            return current['Close'] < previous['Close']

        # CRITICAL FIX: Handle complex MT4 conditions with volume
        elif "High[0] > Close[-1] && volume[0] > volume[-1]" in condition:
            # For now, ignore volume condition since we don't have volume data in all datasets
            # Focus on the price condition
            price_condition = current['High'] > previous['Close']
            print(f"🎯 MT4 Logic: High[0] > Close[-1] (ignoring volume) -> {current['High']} > {previous['Close']} = {price_condition}")
            return price_condition

        # Handle MT4 bullish bar condition
        elif "Close[0] > Open[0]" in condition:
            print(f"🎯 MT4 Logic: Close[0] > Open[0] -> {current['Close']} > {current['Open']} = {current['Close'] > current['Open']}")
            return current['Close'] > current['Open']

        # Handle MT4 bearish bar condition
        elif "Close[0] < Open[0]" in condition:
            print(f"🎯 MT4 Logic: Close[0] < Open[0] -> {current['Close']} < {current['Open']} = {current['Close'] < current['Open']}")
            return current['Close'] < current['Open']

        # CRITICAL FIX: Add simple breakout patterns that should trigger
        elif "close > previous high" in condition:
            return current['Close'] > previous['High']

        elif "high > previous high" in condition:
            return current['High'] > previous['High']

        # Add a very simple test condition that should trigger frequently
        elif "test" in condition or "simple" in condition:
            # Simple condition: close higher than open (bullish bar)
            return current['Close'] > current['Open']
        
        # Handle engulfing patterns
        elif "engulfing" in condition:
            # Bullish engulfing: current close > previous high and current open < previous close
            if "bullish" in condition or current['Close'] > current['Open']:
                return (current['Close'] > previous['High'] and 
                       current['Open'] < previous['Close'])
            # Bearish engulfing
            else:
                return (current['Close'] < previous['Low'] and 
                       current['Open'] > previous['Close'])
        
        # Handle inside bar patterns
        elif "inside bar" in condition:
            # Inside bar: current high < previous high and current low > previous low
            is_inside = (current['High'] < previous['High'] and 
                        current['Low'] > previous['Low'])
            
            # If it's an inside bar, check for breakout direction
            if is_inside and "breakout" in condition:
                # Look for breakout in next few bars (simplified)
                return current['Close'] > previous['High'] or current['Close'] < previous['Low']
            
            return is_inside

        # DEBUG: Show unmatched conditions
        print(f"⚠️  UNMATCHED CONDITION: '{condition}' - Add support for this pattern")
        return False
    
    def _get_previous_day_high(self, history) -> Optional[float]:
        """Get the highest high from the previous day's data"""
        # If we have timeframe data, use the daily data
        if hasattr(self, 'timeframe_data') and self.timeframe_data and '1d' in self.timeframe_data:
            daily_data = self.timeframe_data['1d']
            if len(daily_data) >= 2:
                # Get the previous day's high (second to last day)
                return daily_data.iloc[-2]['High']

        # Fallback to minute data calculation
        minutes_per_day = 1440  # 24 hours * 60 minutes
        if len(history) < minutes_per_day:  # Need at least 1 day of minute data
            return None

        # Look back 1 day to get previous day's high
        prev_day_data = history.iloc[-minutes_per_day:-1]  # Exclude current bar
        return prev_day_data['High'].max()
    
    def _get_previous_day_low(self, history) -> Optional[float]:
        """Get the lowest low from the previous day's data"""
        # If we have timeframe data, use the daily data
        if hasattr(self, 'timeframe_data') and self.timeframe_data and '1d' in self.timeframe_data:
            daily_data = self.timeframe_data['1d']
            if len(daily_data) >= 2:
                # Get the previous day's low (second to last day)
                return daily_data.iloc[-2]['Low']

        # Fallback to minute data calculation
        minutes_per_day = 1440  # 24 hours * 60 minutes
        if len(history) < minutes_per_day:  # Need at least 1 day of minute data
            return None

        # Look back 1 day to get previous day's low
        prev_day_data = history.iloc[-minutes_per_day:-1]  # Exclude current bar
        return prev_day_data['Low'].min()
    
    def _check_time_filter(self, time_filter: str, current) -> bool:
        """Check if current time matches the comprehensive time filter"""
        if not time_filter:
            return True

        # CRITICAL FIX: Handle "All hours" filter
        time_filter_lower = time_filter.lower()
        if "all hours" in time_filter_lower:
            return True

        # Get current time information
        current_hour = current.get('hour', 0) if 'hour' in current.index else 0
        current_datetime = current.get('datetime') if 'datetime' in current.index else None

        # Parse the complex time filter: "Wednesday, Hours 9:00-11:00, Candle position 2-4 since open"

        # 1. Check Day of Week
        day_match = self._check_day_of_week_filter(time_filter_lower, current_datetime)
        if not day_match:
            return False

        # 2. Check Hour Range
        hour_match = self._check_hour_range_filter(time_filter_lower, current_hour)
        if not hour_match:
            return False

        # 3. Check Candle Position (if specified)
        candle_position_match = self._check_candle_position_filter(time_filter_lower, current_datetime)
        if not candle_position_match:
            return False

        return True

    def _check_day_of_week_filter(self, time_filter: str, current_datetime) -> bool:
        """Check day of week filter"""
        if not current_datetime:
            return True  # No datetime available, skip day filter

        # Extract day of week from filter
        days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']
        for day in days:
            if day in time_filter:
                current_day = current_datetime.strftime('%A').lower()
                return current_day == day

        return True  # No day filter specified

    def _check_hour_range_filter(self, time_filter: str, current_hour: int) -> bool:
        """Check hour range filter including MT4 format"""
        # Handle MT4 format: "Hour() >= 9 && Hour() <= 16"
        mt4_range_match = re.search(r'hour\(\)\s*>=\s*(\d+)\s*&&\s*hour\(\)\s*<=\s*(\d+)', time_filter.lower())
        if mt4_range_match:
            start_hour = int(mt4_range_match.group(1))
            end_hour = int(mt4_range_match.group(2))
            return start_hour <= current_hour <= end_hour

        # Handle MT4 complex format: "Hour() >= 23 && Hour() == 0 || Hour() <= 5"
        # This is a complex condition, let's simplify it for now
        if 'hour()' in time_filter.lower() and ('&&' in time_filter or '||' in time_filter):
            # Extract all hour numbers and create a reasonable range
            hour_numbers = re.findall(r'(\d+)', time_filter)
            if hour_numbers:
                # For complex conditions, be permissive and allow most hours
                return True

        # Handle "Hours 9:00-11:00" format
        hour_range_match = re.search(r'hours?\s*(\d{1,2}):00\s*-\s*(\d{1,2}):00', time_filter)
        if hour_range_match:
            start_hour = int(hour_range_match.group(1))
            end_hour = int(hour_range_match.group(2))
            return start_hour <= current_hour <= end_hour

        # Handle "9:00 to 11:00" format
        hour_match = re.search(r'(\d{1,2}):00\s*to\s*(\d{1,2}):00', time_filter)
        if hour_match:
            start_hour = int(hour_match.group(1))
            end_hour = int(hour_match.group(2))
            return start_hour <= current_hour <= end_hour

        # Extract all hours and use as range
        hours_match = re.findall(r'(\d{1,2}):00', time_filter)
        if len(hours_match) >= 2:
            start_hour = int(hours_match[0])
            end_hour = int(hours_match[-1])
            return start_hour <= current_hour <= end_hour
        elif len(hours_match) == 1:
            allowed_hour = int(hours_match[0])
            return current_hour == allowed_hour

        return True  # No hour filter specified

    def _check_candle_position_filter(self, time_filter: str, current_datetime) -> bool:
        """Check candle position since market open filter"""
        if not current_datetime:
            return True  # No datetime available, skip position filter

        # Handle "Candle position 2-4 since open"
        position_range_match = re.search(r'candle position (\d+)-(\d+) since open', time_filter)
        if position_range_match:
            min_pos = int(position_range_match.group(1))
            max_pos = int(position_range_match.group(2))
            current_position = self._calculate_candle_position_since_open(current_datetime)
            return min_pos <= current_position <= max_pos

        # Handle "Candle position 7 since open"
        position_exact_match = re.search(r'candle position (\d+) since open', time_filter)
        if position_exact_match:
            target_pos = int(position_exact_match.group(1))
            current_position = self._calculate_candle_position_since_open(current_datetime)
            return current_position == target_pos

        # Handle "Candle position 10+ since open"
        position_min_match = re.search(r'candle position (\d+)\+ since open', time_filter)
        if position_min_match:
            min_pos = int(position_min_match.group(1))
            current_position = self._calculate_candle_position_since_open(current_datetime)
            return current_position >= min_pos

        return True  # No position filter specified

    def _calculate_candle_position_since_open(self, current_datetime) -> int:
        """Calculate candle position since market open (more realistic interpretation)"""
        try:
            # For realistic trading, interpret "candle position X since open" as:
            # Position relative to the start of the current trading session

            # Get the start of the current trading day (9:30 AM)
            market_open = current_datetime.replace(hour=9, minute=30, second=0, microsecond=0)

            # If current time is before market open, use previous day's session
            if current_datetime < market_open:
                market_open = market_open - timedelta(days=1)

            # Calculate time difference in minutes
            time_diff = current_datetime - market_open
            minutes_since_open = int(time_diff.total_seconds() / 60)

            # For pattern matching, use a more reasonable interpretation:
            # "Position 2-4" means "early in the session" (first 30-60 minutes)
            # "Position 7" means "mid-morning" (around 1-2 hours after open)
            # "Position 10+" means "later in session" (2+ hours after open)

            if minutes_since_open <= 60:  # First hour
                return min(4, max(1, minutes_since_open // 15 + 1))  # Positions 1-4
            elif minutes_since_open <= 120:  # Second hour
                return min(8, 5 + (minutes_since_open - 60) // 15)  # Positions 5-8
            else:  # Later in session
                return min(15, 9 + (minutes_since_open - 120) // 30)  # Positions 9-15

        except Exception as e:
            print(f"Warning: Candle position calculation failed: {e}")
            return 1  # Default to position 1 if calculation fails

    def _evaluate_cross_timeframe_condition(self, condition: str, current, history) -> bool:
        """Evaluate cross-timeframe conditions like '15-minute candle is bullish'"""
        # Handle "X-minute candle is bullish"
        bullish_match = re.search(r'(\d+)-minute candle is bullish', condition)
        if bullish_match:
            timeframe_minutes = int(bullish_match.group(1))
            return self._check_timeframe_candle_bullish(history, current, timeframe_minutes)

        # Handle "X-minute candle is bearish"
        bearish_match = re.search(r'(\d+)-minute candle is bearish', condition)
        if bearish_match:
            timeframe_minutes = int(bearish_match.group(1))
            return self._check_timeframe_candle_bearish(history, current, timeframe_minutes)

        # Handle "1-hour candle is bullish"
        if "1-hour candle is bullish" in condition or "1h candle is bullish" in condition:
            return self._check_timeframe_candle_bullish(history, current, 60)

        # Handle "1-hour candle is bearish"
        if "1-hour candle is bearish" in condition or "1h candle is bearish" in condition:
            return self._check_timeframe_candle_bearish(history, current, 60)

        # Handle "30-minute candle is bullish"
        if "30-minute candle is bullish" in condition:
            return self._check_timeframe_candle_bullish(history, current, 30)

        return True  # Default to true if condition not recognized

    def _check_timeframe_breakout(self, history, current, timeframe_minutes: int, price_type: str, operator: str) -> bool:
        """Check if current price breaks previous timeframe high/low"""
        try:
            # Use timeframe data if available
            if hasattr(self, 'timeframe_data') and self.timeframe_data:
                timeframe_key = f"{timeframe_minutes}min"
                if timeframe_key in self.timeframe_data:
                    tf_data = self.timeframe_data[timeframe_key]
                    if len(tf_data) >= 2:
                        # CRITICAL FIX: Capitalize price_type to match DataFrame columns
                        price_column = price_type.capitalize()  # 'high' -> 'High', 'low' -> 'Low'
                        previous_value = tf_data.iloc[-2][price_column]  # Previous timeframe candle
                        if operator == '>':
                            return current['Close'] > previous_value
                        else:
                            return current['Close'] < previous_value

            # Fallback to minute data calculation
            lookback_bars = max(1, timeframe_minutes)
            if len(history) >= lookback_bars + 1:
                lookback_data = history.iloc[-lookback_bars-1:-1]
                if price_type == 'high':
                    target_value = lookback_data['High'].max()
                else:
                    target_value = lookback_data['Low'].min()

                if operator == '>':
                    return current['Close'] > target_value
                else:
                    return current['Close'] < target_value

        except Exception as e:
            print(f"Warning: Timeframe breakout check failed: {e}")

        return False

    def _check_timeframe_candle_bullish(self, history, current, timeframe_minutes: int) -> bool:
        """Check if the specified timeframe candle is bullish"""
        try:
            # Use timeframe data if available
            if hasattr(self, 'timeframe_data') and self.timeframe_data:
                timeframe_key = f"{timeframe_minutes}min"
                if timeframe_key in self.timeframe_data:
                    tf_data = self.timeframe_data[timeframe_key]
                    if len(tf_data) >= 1:
                        latest_candle = tf_data.iloc[-1]
                        return latest_candle['Close'] > latest_candle['Open']

            # Fallback to minute data calculation
            lookback_bars = timeframe_minutes
            if len(history) >= lookback_bars:
                tf_data = history.iloc[-lookback_bars:]
                tf_open = tf_data.iloc[0]['Open']
                tf_close = tf_data.iloc[-1]['Close']
                return tf_close > tf_open

        except Exception as e:
            print(f"Warning: Timeframe bullish check failed: {e}")

        return False

    def _check_timeframe_candle_bearish(self, history, current, timeframe_minutes: int) -> bool:
        """Check if the specified timeframe candle is bearish"""
        try:
            # Use timeframe data if available
            if hasattr(self, 'timeframe_data') and self.timeframe_data:
                timeframe_key = f"{timeframe_minutes}min"
                if timeframe_key in self.timeframe_data:
                    tf_data = self.timeframe_data[timeframe_key]
                    if len(tf_data) >= 1:
                        latest_candle = tf_data.iloc[-1]
                        return latest_candle['Close'] < latest_candle['Open']

            # Fallback to minute data calculation
            lookback_bars = timeframe_minutes
            if len(history) >= lookback_bars:
                tf_data = history.iloc[-lookback_bars:]
                tf_open = tf_data.iloc[0]['Open']
                tf_close = tf_data.iloc[-1]['Close']
                return tf_close < tf_open

        except Exception as e:
            print(f"Warning: Timeframe bearish check failed: {e}")

        return False
    
    def _calculate_stop_loss(self, stop_loss_rule: str, entry_price: float, current, previous, direction: str = 'long') -> Optional[float]:
        """Calculate stop loss price with cross-timeframe support"""
        stop_loss_rule_lower = stop_loss_rule.lower()

        # CRITICAL FIX: Check original case first, then lowercase for LLM compatibility
        # LLM often generates "Low[1]" (capital) but we need to handle both cases

        # CRITICAL FIX: Handle MT4 syntax first (most common from LLM)
        # Handle MT4 syntax like "Low[1]", "High[1]", etc. - check original case for MT4 syntax
        if "Low[1]" in stop_loss_rule:
            print(f"🎯 MT4 Stop Loss: Low[1] -> {previous['Low']}")
            return previous['Low']
        elif "High[1]" in stop_loss_rule:
            print(f"🎯 MT4 Stop Loss: High[1] -> {previous['High']}")
            return previous['High']
        elif "Low[0]" in stop_loss_rule:
            print(f"🎯 MT4 Stop Loss: Low[0] -> {current['Low']}")
            return current['Low']
        elif "High[0]" in stop_loss_rule:
            print(f"🎯 MT4 Stop Loss: High[0] -> {current['High']}")
            return current['High']
        elif "Close[1]" in stop_loss_rule:
            print(f"🎯 MT4 Stop Loss: Close[1] -> {previous['Close']}")
            return previous['Close']
        elif "Open[1]" in stop_loss_rule:
            print(f"🎯 MT4 Stop Loss: Open[1] -> {previous['Open']}")
            return previous['Open']

        # CRITICAL FIX: Add support for bars beyond [1] - needed for LLM patterns
        elif "Low[2]" in stop_loss_rule:
            # This requires access to the full data and bar index
            # For now, return previous low as fallback
            print(f"🎯 MT4 Stop Loss: Low[2] -> {previous['Low']} (using Low[1] as fallback)")
            return previous['Low']
        elif "High[2]" in stop_loss_rule:
            print(f"🎯 MT4 Stop Loss: High[2] -> {previous['High']} (using High[1] as fallback)")
            return previous['High']

        # FIXED: Handle LM Studio format like "Below 15-minute candle low" - NO FALLBACKS
        # Check for timeframe reference - must work or fail
        tf_low_match = re.search(r'below (\d+)-minute candle low', stop_loss_rule_lower)
        if tf_low_match:
            timeframe_minutes = int(tf_low_match.group(1))
            tf_low = self._get_timeframe_candle_low(timeframe_minutes)
            if tf_low is not None:
                return tf_low
            else:
                raise RuleParseError(f"LLM specified {timeframe_minutes}-minute timeframe but data not available. Got: '{stop_loss_rule}'")

        # Handle "Above X-minute candle high" - NO FALLBACKS
        tf_high_match = re.search(r'above (\d+)-minute candle high', stop_loss_rule_lower)
        if tf_high_match:
            timeframe_minutes = int(tf_high_match.group(1))
            tf_high = self._get_timeframe_candle_high(timeframe_minutes)
            if tf_high is not None:
                return tf_high
            else:
                raise RuleParseError(f"LLM specified {timeframe_minutes}-minute timeframe but data not available. Got: '{stop_loss_rule}'")

        # Handle percentage-based stop loss like "0.4% below entry"
        pct_match = re.search(r'(\d+\.?\d*)%', stop_loss_rule_lower)
        if pct_match:
            pct = float(pct_match.group(1)) / 100
            if "below" in stop_loss_rule_lower:
                return entry_price * (1 - pct)
            elif "above" in stop_loss_rule_lower:
                return entry_price * (1 + pct)

        # Handle "Below previous low" or "Above previous high"
        if "below previous low" in stop_loss_rule_lower or "previous low" in stop_loss_rule_lower:
            return previous['Low']
        elif "above previous high" in stop_loss_rule_lower or "previous high" in stop_loss_rule_lower:
            return previous['High']

        # FIXED: NO FALLBACKS - LLM must provide complete rules
        # If we reach here, the LLM didn't provide a valid stop loss rule
        raise RuleParseError(f"LLM must provide a valid stop loss rule. Got: '{stop_loss_rule}'")

    def _get_timeframe_candle_low(self, timeframe_minutes: int) -> Optional[float]:
        """Get the low of the current timeframe candle"""
        try:
            # Use timeframe data if available
            if hasattr(self, 'timeframe_data') and self.timeframe_data:
                timeframe_key = f"{timeframe_minutes}min"
                if timeframe_key in self.timeframe_data:
                    tf_data = self.timeframe_data[timeframe_key]
                    if len(tf_data) >= 1:
                        return tf_data.iloc[-1]['Low']
        except Exception as e:
            print(f"Warning: Could not get {timeframe_minutes}-minute candle low: {e}")
        return None

    def _get_timeframe_candle_high(self, timeframe_minutes: int) -> Optional[float]:
        """Get the high of the current timeframe candle"""
        try:
            # Use timeframe data if available
            if hasattr(self, 'timeframe_data') and self.timeframe_data:
                timeframe_key = f"{timeframe_minutes}min"
                if timeframe_key in self.timeframe_data:
                    tf_data = self.timeframe_data[timeframe_key]
                    if len(tf_data) >= 1:
                        return tf_data.iloc[-1]['High']
        except Exception as e:
            print(f"Warning: Could not get {timeframe_minutes}-minute candle high: {e}")
        return None
    
    def _calculate_take_profit(self, exit_condition: str, entry_price: float, stop_loss_price: Optional[float], direction: str = 'long') -> Optional[float]:
        """Calculate take profit price with support for complex mathematical expressions"""
        exit_condition_lower = exit_condition.lower()

        # FIXED: Handle complex mathematical expressions like "Close[0] + (Close[0] - Low[1]) * 2.0"
        # Check original case for MT4 syntax - accept High[1], Low[1], or Low[2]
        if "Close[0]" in exit_condition and ("Low[1]" in exit_condition or "Low[2]" in exit_condition or "High[1]" in exit_condition):
            try:
                # Extract the mathematical expression and evaluate it
                # Pattern: Close[0] + (Close[0] - Low[1]) * multiplier OR Close[0] + (Close[0] - High[1]) * multiplier
                multiplier_match = re.search(r'\*\s*(\d+\.?\d*)', exit_condition_lower)
                if multiplier_match:
                    multiplier = float(multiplier_match.group(1))
                    # Calculate: entry_price + (entry_price - stop_loss_price) * multiplier
                    if stop_loss_price is not None:
                        risk = abs(entry_price - stop_loss_price)
                        if direction == 'long':
                            tp_price = entry_price + (risk * multiplier)
                            # CRITICAL FIX: Validate TP relationship for LONG trades
                            if tp_price <= entry_price:
                                min_tp_distance = max(risk * 0.5, 1.0)  # At least 0.5R or 1 point
                                tp_price = entry_price + min_tp_distance
                                print(f"🔧 Fixed TP for LONG: {tp_price} (was {entry_price + (risk * multiplier)})")
                            return tp_price
                        else:  # short
                            tp_price = entry_price - (risk * multiplier)
                            # CRITICAL FIX: Validate TP relationship for SHORT trades
                            if tp_price >= entry_price:
                                min_tp_distance = max(risk * 0.5, 1.0)  # At least 0.5R or 1 point
                                tp_price = entry_price - min_tp_distance
                                print(f"🔧 Fixed TP for SHORT: {tp_price} (was {entry_price - (risk * multiplier)})")
                            return tp_price
            except Exception as e:
                print(f"Warning: Complex take profit calculation failed: {e}")

        # Handle percentage-based take profit like "0.5% profit target"
        pct_match = re.search(r'(\d+\.?\d*)%', exit_condition_lower)
        if pct_match:
            pct = float(pct_match.group(1)) / 100
            if "profit" in exit_condition_lower or "gain" in exit_condition_lower:
                if direction == 'long':
                    tp_price = entry_price * (1 + pct)
                    # CRITICAL FIX: Validate minimum percentage for LONG trades
                    if pct < 0.001:  # Less than 0.1%
                        tp_price = entry_price * 1.01  # Force 1% minimum
                        print(f"🔧 Fixed TP percentage for LONG: {tp_price} (minimum 1%)")
                    return tp_price
                else:  # short
                    tp_price = entry_price * (1 - pct)
                    # CRITICAL FIX: Validate minimum percentage for SHORT trades
                    if pct < 0.001:  # Less than 0.1%
                        tp_price = entry_price * 0.99  # Force 1% minimum
                        print(f"🔧 Fixed TP percentage for SHORT: {tp_price} (minimum 1%)")
                    return tp_price
            elif "loss" in exit_condition_lower:
                if direction == 'long':
                    return entry_price * (1 - pct)
                else:  # short
                    return entry_price * (1 + pct)

        # Handle R-multiple targets (e.g., "2R", "3R")
        r_match = re.search(r'(\d+\.?\d*)R', exit_condition_lower)
        if r_match and stop_loss_price:
            r_multiple = float(r_match.group(1))
            risk = abs(entry_price - stop_loss_price)
            if direction == 'long':
                tp_price = entry_price + (r_multiple * risk)  # For long trades
                # CRITICAL FIX: Validate minimum R-multiple for LONG trades
                if r_multiple < 0.5:
                    tp_price = entry_price + (0.5 * risk)  # Force 0.5R minimum
                    print(f"🔧 Fixed TP R-multiple for LONG: {tp_price} (0.5R minimum)")
                return tp_price
            else:  # short
                tp_price = entry_price - (r_multiple * risk)  # For short trades
                # CRITICAL FIX: Validate minimum R-multiple for SHORT trades
                if r_multiple < 0.5:
                    tp_price = entry_price - (0.5 * risk)  # Force 0.5R minimum
                    print(f"🔧 Fixed TP R-multiple for SHORT: {tp_price} (0.5R minimum)")
                return tp_price

        # FIXED: NO FALLBACKS - LLM must provide complete rules
        # If we reach here, the LLM didn't provide a valid take profit rule
        raise RuleParseError(f"LLM must provide a valid take profit rule. Got: '{exit_condition}'")
    
    def _extract_max_duration(self, exit_condition: str) -> Optional[int]:
        """Extract maximum duration in minutes"""
        # Handle LLM format like "After 90 minutes"
        after_min_match = re.search(r'after\s+(\d+)\s*minutes?', exit_condition.lower())
        if after_min_match:
            return int(after_min_match.group(1))

        hour_match = re.search(r'(\d+)\s*hours?', exit_condition.lower())
        if hour_match:
            return int(hour_match.group(1)) * 60

        min_match = re.search(r'(\d+)\s*min', exit_condition.lower())
        if min_match:
            return int(min_match.group(1))

        # Default to 120 minutes if no duration specified
        return 120

    def _is_mt4_condition(self, condition: str) -> bool:
        """Check if condition uses MT4 syntax like Close[0], High[1], etc."""
        import re
        return bool(re.search(r'\b(Close|High|Low|Open)\[\d+\]', condition))

    def _evaluate_mt4_condition(self, condition: str, current, previous, history) -> bool:
        """Evaluate MT4 conditions with support for simple patterns"""

        # Handle simple MT4 conditions first
        if "Close[0] > High[1]" in condition:
            result = current['Close'] > previous['High']
            print(f"🎯 MT4 Logic: Close[0] > High[1] -> {current['Close']} > {previous['High']} = {result}")
            return result

        elif "Close[0] < High[1]" in condition:
            result = current['Close'] < previous['High']
            print(f"🎯 MT4 Logic: Close[0] < High[1] -> {current['Close']} < {previous['High']} = {result}")
            return result

        elif "Close[0] > Low[1]" in condition:
            result = current['Close'] > previous['Low']
            print(f"🎯 MT4 Logic: Close[0] > Low[1] -> {current['Close']} > {previous['Low']} = {result}")
            return result

        elif "Close[0] < Low[1]" in condition:
            result = current['Close'] < previous['Low']
            print(f"🎯 MT4 Logic: Close[0] < Low[1] -> {current['Close']} < {previous['Low']} = {result}")
            return result

        elif "Close[0] > Open[1]" in condition:
            result = current['Close'] > previous['Open']
            print(f"🎯 MT4 Logic: Close[0] > Open[1] -> {current['Close']} > {previous['Open']} = {result}")
            return result

        elif "Close[0] < Open[1]" in condition:
            result = current['Close'] < previous['Open']
            print(f"🎯 MT4 Logic: Close[0] < Open[1] -> {current['Close']} < {previous['Open']} = {result}")
            return result

        elif "High[0] > High[1]" in condition:
            result = current['High'] > previous['High']
            print(f"🎯 MT4 Logic: High[0] > High[1] -> {current['High']} > {previous['High']} = {result}")
            return result

        elif "Low[0] < Low[1]" in condition:
            result = current['Low'] < previous['Low']
            print(f"🎯 MT4 Logic: Low[0] < Low[1] -> {current['Low']} < {previous['Low']} = {result}")
            return result

        # Handle complex conditions with && - evaluate first part only for now
        elif "&&" in condition:
            # Split on && and evaluate first condition
            first_condition = condition.split("&&")[0].strip()
            print(f"🎯 Complex MT4 condition detected, evaluating first part: {first_condition}")
            return self._evaluate_mt4_condition(first_condition, current, previous, history)

        # If no match found, log and return False
        print(f"⚠️  UNMATCHED MT4 CONDITION: '{condition}' - Add support for this pattern")
        return False

    def generate_mt4_ea_code(self, ea_name: str = "LLM_Generated_EA") -> str:
        """Generate MT4 Expert Advisor code"""
        
        mt4_code = f"""//+------------------------------------------------------------------+
//|                                                {ea_name}.mq4 |
//|                        Generated by LLM Pattern Discovery System |
//|                                                                  |
//+------------------------------------------------------------------+
#property copyright "LLM Pattern Discovery"
#property link      ""
#property version   "1.00"
#property strict

// Input parameters - NO DEFAULTS, LLM CONTROLS EVERYTHING
input double LotSize = 0.1;
input int MagicNumber = 12345;
input bool UseTimeFilter = true;

// Risk Management Parameters - FROM CONFIG ONLY
input bool EnableRiskManagement = true;  // Enable risk management
input double MaxDailyLossPct = 2.0;      // Maximum daily loss percentage
input double MaxRiskPerTradePct = 2.0;   // Maximum risk per trade percentage
input int MaxConsecutiveLosses = 5;      // Maximum consecutive losses
input double AccountBalance = 100000;    // Account balance for risk calculations

// Pattern Toggle Parameters - Enable/Disable Individual Patterns"""

        # Add toggle parameters for each rule
        for rule in self.rules:
            mt4_code += f"""
input bool EnablePattern{rule.rule_id} = true;  // Enable Pattern {rule.rule_id}"""

        mt4_code += """

// Global variables
datetime lastBarTime = 0;
double dailyPnL = 0.0;
datetime lastTradeDate = 0;
int consecutiveLosses = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{{
   return(INIT_SUCCEEDED);
}}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{{
   // Check for new bar
   if(Time[0] == lastBarTime)
      return;
   lastBarTime = Time[0];

   // Reset daily P&L if new day
   if(EnableRiskManagement && TimeDay(Time[0]) != TimeDay(lastTradeDate))
   {{
      dailyPnL = 0.0;
      Print("New trading day - Daily P&L reset");
   }}

   // Check risk management limits
   if(EnableRiskManagement && !CheckRiskLimits())
      return;

   // Check existing positions
   if(OrdersTotal() > 0)
      return;

   // Apply trading rules (only if enabled)
"""

        # Add each rule as a separate function call with toggle check
        for rule in self.rules:
            mt4_code += f"   if(EnablePattern{rule.rule_id}) CheckRule{rule.rule_id}();\n"
        
        mt4_code += "}\n\n"
        
        # Generate individual rule functions
        for rule in self.rules:
            mt4_code += self._generate_mt4_rule_function(rule)
        
        # Add utility functions
        mt4_code += self._generate_mt4_utility_functions()
        
        return mt4_code
    
    def _generate_mt4_rule_function(self, rule: TradingRule) -> str:
        """Generate MT4 function for a single rule"""

        # Generate stop loss and take profit calculations
        stop_loss_calc = self._get_mt4_stop_loss(rule)
        take_profit_calc = self._get_mt4_take_profit(rule)

        func_code = f"""
//+------------------------------------------------------------------+
//| Rule {rule.rule_id}: {rule.entry_condition[:50]}...
//+------------------------------------------------------------------+
void CheckRule{rule.rule_id}()
{{
   // Time filter check
   if(UseTimeFilter && !CheckTimeFilter{rule.rule_id}())
      return;

   // Entry condition check
   if(!CheckEntryCondition{rule.rule_id}())
      return;

   // Calculate prices
   double entryPrice = {self._get_mt4_entry_price(rule)};
   double stopLoss = {stop_loss_calc};
   double takeProfit = {take_profit_calc};

   // Normalize prices
   entryPrice = NormalizeDouble(entryPrice, Digits);
   stopLoss = NormalizeDouble(stopLoss, Digits);
   takeProfit = NormalizeDouble(takeProfit, Digits);

   // Validate prices are reasonable
   double minDistance = MarketInfo(Symbol(), MODE_STOPLEVEL) * Point;
   if(MathAbs(entryPrice - stopLoss) < minDistance || MathAbs(entryPrice - takeProfit) < minDistance)
   {{
      Print("Rule{rule.rule_id}: Stop/Target too close to entry price");
      return;
   }}

   // Calculate position size with risk management
   double positionSize = CalculatePositionSize(entryPrice, stopLoss);

   // Place order
   int orderType = {self._get_mt4_order_type(rule)};
   int ticket = OrderSend(Symbol(), orderType, positionSize, entryPrice, 3, stopLoss, takeProfit,
                         "Rule{rule.rule_id}", MagicNumber, 0, clrBlue);

   if(ticket < 0)
      Print("Rule{rule.rule_id} Order failed: ", GetLastError());
   else
      Print("Rule{rule.rule_id} Order placed successfully: ", ticket);
}}

bool CheckTimeFilter{rule.rule_id}()
{{
   {self._generate_mt4_time_filter(rule)}
}}

bool CheckEntryCondition{rule.rule_id}()
{{
   {self._generate_mt4_entry_condition(rule)}
}}
"""
        return func_code
    
    def _get_mt4_entry_price(self, rule: TradingRule) -> str:
        """Get MT4 entry price expression"""
        if rule.direction == 'long':
            return "Ask"
        else:
            return "Bid"
    
    def _get_mt4_order_type(self, rule: TradingRule) -> str:
        """Get MT4 order type"""
        if rule.direction == 'long':
            return "OP_BUY"
        else:
            return "OP_SELL"
    
    def _get_mt4_stop_loss(self, rule: TradingRule) -> str:
        """Generate MT4 stop loss calculation"""
        # Extract percentage from stop loss rule
        pct_match = re.search(r'(\d+\.?\d*)%', rule.stop_loss)
        if pct_match:
            pct = float(pct_match.group(1)) / 100
            if rule.direction == 'long':
                return f"entryPrice * {1 - pct:.6f}"
            else:
                return f"entryPrice * {1 + pct:.6f}"
        elif "previous low" in rule.stop_loss.lower():
            return "Low[1]"
        elif "previous high" in rule.stop_loss.lower():
            return "High[1]"
        else:
            # FIXED: NO FALLBACKS - LLM must provide complete rules
            raise RuleParseError(f"LLM must provide a valid stop loss rule. Got: '{rule.stop_loss}'")
    
    def _get_mt4_take_profit(self, rule: TradingRule) -> str:
        """Generate MT4 take profit calculation"""
        # Extract percentage from exit condition
        pct_match = re.search(r'(\d+\.?\d*)%', rule.exit_condition)
        if pct_match:
            pct = float(pct_match.group(1)) / 100
            if rule.direction == 'long':
                return f"entryPrice * {1 + pct:.6f}"
            else:
                return f"entryPrice * {1 - pct:.6f}"

        # Extract R-multiple (e.g., "3R", "2.5R")
        r_match = re.search(r'(\d+\.?\d*)R', rule.exit_condition)
        if r_match:
            r_multiple = float(r_match.group(1))
            if rule.direction == 'long':
                return f"entryPrice + {r_multiple} * (entryPrice - stopLoss)"
            else:
                return f"entryPrice - {r_multiple} * (stopLoss - entryPrice)"

        # FIXED: NO FALLBACKS - LLM must provide complete rules
        raise RuleParseError(f"LLM must provide a valid take profit rule. Got: '{rule.exit_condition}'")
    
    def _generate_mt4_time_filter(self, rule: TradingRule) -> str:
        """Generate MT4 time filter code"""
        if not rule.time_filters:
            return "return true;"
        
        # Extract hours from time filter
        hours = re.findall(r'(\d{1,2}):00', rule.time_filters)
        if len(hours) >= 2:
            start_hour = hours[0]
            end_hour = hours[-1]
            return f"int currentHour = Hour(); return (currentHour >= {start_hour} && currentHour <= {end_hour});"
        elif len(hours) == 1:
            hour = hours[0]
            return f"return (Hour() == {hour});"
        
        return "return true;"
    
    def _generate_mt4_entry_condition(self, rule: TradingRule) -> str:
        """Generate MT4 entry condition code with enhanced breakout patterns"""
        condition = rule.entry_condition.lower()

        # Previous day high/low breakouts
        if "close > previous day's high" in condition or "break above previous day" in condition:
            pct_match = re.search(r'(\d+\.?\d*)%', condition)
            if pct_match:
                pct = float(pct_match.group(1)) / 100
                return f"return (Close[0] > iHigh(Symbol(), PERIOD_D1, 1) * {1 + pct:.6f});"
            else:
                return f"return (Close[0] > iHigh(Symbol(), PERIOD_D1, 1));"

        elif "close < previous day's low" in condition or "break below previous day" in condition:
            pct_match = re.search(r'(\d+\.?\d*)%', condition)
            if pct_match:
                pct = float(pct_match.group(1)) / 100
                return f"return (Close[0] < iLow(Symbol(), PERIOD_D1, 1) * {1 - pct:.6f});"
            else:
                return f"return (Close[0] < iLow(Symbol(), PERIOD_D1, 1));"

        # Close breakouts (most common)
        elif "close > previous high" in condition:
            pct_match = re.search(r'(\d+\.?\d*)%', condition)
            if pct_match:
                pct = float(pct_match.group(1)) / 100
                return f"return (Close[0] > High[1] * {1 + pct:.6f});"
            else:
                return f"return (Close[0] > High[1]);"

        elif "close < previous low" in condition:
            pct_match = re.search(r'(\d+\.?\d*)%', condition)
            if pct_match:
                pct = float(pct_match.group(1)) / 100
                return f"return (Close[0] < Low[1] * {1 - pct:.6f});"
            else:
                return f"return (Close[0] < Low[1]);"

        # Previous bar high/low breakouts
        elif "high > previous high" in condition or "break above previous high" in condition:
            pct_match = re.search(r'(\d+\.?\d*)%', condition)
            if pct_match:
                pct = float(pct_match.group(1)) / 100
                return f"return (High[0] > High[1] * {1 + pct:.6f});"
            else:
                return f"return (High[0] > High[1]);"

        elif "low < previous low" in condition or "break below previous low" in condition:
            pct_match = re.search(r'(\d+\.?\d*)%', condition)
            if pct_match:
                pct = float(pct_match.group(1)) / 100
                return f"return (Low[0] < Low[1] * {1 - pct:.6f});"
            else:
                return f"return (Low[0] < Low[1]);"

        # Range breakouts
        elif "breakout" in condition and ("range" in condition or "consolidation" in condition):
            return f"return (High[0] > High[1] && Close[0] > High[1]);"

        # Inside bar breakouts
        elif "inside bar" in condition and "breakout" in condition:
            return f"return (High[1] < High[2] && Low[1] > Low[2] && (High[0] > High[1] || Low[0] < Low[1]));"

        # Strong close patterns
        elif "strong close" in condition and ("upper" in condition or "75%" in condition):
            return f"return (Close[0] > (High[0] - (High[0] - Low[0]) * 0.25));"

        # Engulfing patterns
        elif "engulfing" in condition:
            if "bullish" in condition:
                return f"return (Close[0] > Open[0] && Close[1] < Open[1] && Open[0] < Close[1] && Close[0] > Open[1]);"
            else:
                return f"return (Close[0] < Open[0] && Close[1] > Open[1] && Open[0] > Close[1] && Close[0] < Open[1]);"

        # Three white soldiers pattern
        elif "three white soldiers" in condition or "three consecutive" in condition:
            return f"return (Close[0] > Close[1] && Close[1] > Close[2] && Close[2] > Close[3]);"

        # Generic breakout patterns with unique identifiers
        elif "breakout" in condition or "break" in condition:
            # Use rule ID to create unique conditions
            return f"data['Close'].iloc[-1] > data['High'].iloc[-2]"  # Simple breakout condition

        # FIXED: NO FALLBACKS - LLM must provide complete rules
        raise RuleParseError(f"LLM must provide a recognizable entry condition. Got: '{rule.entry_condition}'")
    
    def _generate_mt4_utility_functions(self) -> str:
        """Generate utility functions for MT4 EA"""
        return """
//+------------------------------------------------------------------+
//| Utility Functions                                                |
//+------------------------------------------------------------------+
double NormalizePrice(double price)
{
   return NormalizeDouble(price, Digits);
}

bool IsNewBar()
{
   static datetime lastBarTime = 0;
   if(Time[0] != lastBarTime)
   {
      lastBarTime = Time[0];
      return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| Risk Management Functions                                        |
//+------------------------------------------------------------------+
bool CheckRiskLimits()
{
   if(!EnableRiskManagement)
      return true;

   // Check daily loss limit
   double dailyLossPct = (dailyPnL / AccountBalance) * 100;
   if(dailyLossPct <= -MaxDailyLossPct)
   {
      Print("Daily loss limit exceeded: ", dailyLossPct, "%");
      return false;
   }

   // Check consecutive losses
   if(consecutiveLosses >= MaxConsecutiveLosses)
   {
      Print("Consecutive loss limit exceeded: ", consecutiveLosses);
      return false;
   }

   return true;
}

double CalculatePositionSize(double entryPrice, double stopLoss)
{
   if(!EnableRiskManagement)
      return LotSize;

   double riskAmount = MathAbs(entryPrice - stopLoss);
   if(riskAmount == 0)
      return LotSize;

   double maxRiskMoney = AccountBalance * (MaxRiskPerTradePct / 100);
   double suggestedSize = maxRiskMoney / riskAmount;

   // Don't exceed the input lot size
   return MathMin(suggestedSize, LotSize);
}

void UpdateTradeResult(bool isWin, double pnl)
{
   if(!EnableRiskManagement)
      return;

   dailyPnL += pnl;
   lastTradeDate = Time[0];

   if(isWin)
      consecutiveLosses = 0;
   else
      consecutiveLosses++;

   Print("Trade result updated - Daily P&L: ", dailyPnL, ", Consecutive losses: ", consecutiveLosses);
}
"""

def extract_individual_patterns(llm_response: str) -> List[str]:
    """
    Extract individual pattern texts from LLM response for separate testing
    Each pattern should be tested individually with its specific instructions
    """
    if not llm_response or not isinstance(llm_response, str):
        return []

    # Split by pattern markers - handle different formats
    pattern_markers = ['**Pattern', '### Pattern', '**PATTERN', '### PATTERN']

    # Find all pattern sections
    pattern_sections = []
    lines = llm_response.split('\n')
    current_pattern = []

    for line in lines:
        # Check if this line starts a new pattern (case insensitive)
        line_upper = line.upper()
        is_pattern_start = any(marker.upper() in line_upper for marker in pattern_markers)

        if is_pattern_start:
            # Save previous pattern if we have one (including header + content)
            if current_pattern:
                pattern_text = '\n'.join(current_pattern).strip()
                if pattern_text and len(pattern_text) > 50:  # Only add substantial patterns
                    pattern_sections.append(pattern_text)

            # Start new pattern with this header line
            current_pattern = [line]
        else:
            # Add line to current pattern (whether it's content or empty line)
            if current_pattern:  # Only add if we're inside a pattern
                current_pattern.append(line)

    # Add the last pattern
    if current_pattern:
        pattern_text = '\n'.join(current_pattern).strip()
        if pattern_text and len(pattern_text) > 50:  # Only add substantial patterns
            pattern_sections.append(pattern_text)

    # Debug: Print extracted patterns
    print(f"🔍 DEBUG: Extracted {len(pattern_sections)} individual patterns:")
    for i, pattern in enumerate(pattern_sections, 1):
        print(f"   Pattern {i}: {len(pattern)} chars - {pattern[:100]}...")

    return pattern_sections

def parse_llm_rule(rule_text: str, timeframe_data=None) -> List[Callable]:
    """
    Main function to parse LLM response and return Python functions
    Compatible with existing backtester interface
    """
    parser = LLMRuleParser()
    parser.timeframe_data = timeframe_data  # Store timeframe data in parser
    try:
        rules = parser.parse_llm_response(rule_text)
        return parser.generate_python_functions()
    except RuleParseError as e:
        print(f"Rule parsing failed: {e}")
        return []

def generate_mt4_ea(rule_text: str, ea_name: str = "LLM_Generated_EA") -> str:
    """
    Generate MT4 Expert Advisor code from LLM response
    """
    parser = LLMRuleParser()
    try:
        rules = parser.parse_llm_response(rule_text)
        return parser.generate_mt4_ea_code(ea_name)
    except RuleParseError as e:
        return f"// Error generating EA: {e}"

# Example usage:
# functions = parse_llm_rule(llm_response_text)
# mt4_code = generate_mt4_ea(llm_response_text, "MyTradingEA")
