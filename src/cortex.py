#!/usr/bin/env python3
"""
Cortex - AI-Powered Pattern Discovery
The central orchestrator that uses local LLM to discover profitable trading patterns and generate executable trading systems
"""

import pandas as pd
import numpy as np
import os
import sys
import json
import logging
from datetime import datetime

# Import configuration first - NO FALLBACK (explicit dependency)
from config import config

# Configure logging using configuration
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(config.LOG_FILE),
        logging.StreamHandler()
    ] if config.LOG_TO_FILE and config.LOG_TO_CONSOLE else [
        logging.FileHandler(config.LOG_FILE)
    ] if config.LOG_TO_FILE else [
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Import modules using relative paths
from ai_integration.lm_studio_client import LMStudioClient
from ai_integration.situational_prompts import SituationalAnalysisPrompts
from situational_validator import SituationalValidator
from fact_checker import LLMFactChecker

class Cortex:
    """
    The central AI orchestrator that coordinates between LLM and Jaeger Python components
    Uses local LLM to analyze trading data and discover profitable patterns
    NO USER INPUT - completely autonomous discovery
    """

    def __init__(self):
        self.ai_client = LMStudioClient()

    def _extract_symbol_from_filename(self, filename):
        """Extract trading symbol from data filename"""
        # Handle different filename patterns
        # Example: "2025.6.17DEUIDXEUR_M1_UTCPlus01-M1-No Session.csv" -> "DEUIDXEUR"

        import re

        # Remove file extension
        base_name = filename.replace('.csv', '').replace('.xlsx', '').replace('.xls', '')

        # Pattern 1: Date followed by symbol (like DEUIDXEUR)
        # Look for pattern: date + symbol + underscore + timeframe
        pattern1 = r'\d{4}\.\d{1,2}\.\d{1,2}([A-Z]{3,10})_'
        match1 = re.search(pattern1, base_name)
        if match1:
            return match1.group(1)

        # Pattern 2: Just symbol at start (like EURUSD_M1_data.csv)
        pattern2 = r'^([A-Z]{3,10})_'
        match2 = re.search(pattern2, base_name)
        if match2:
            return match2.group(1)

        # Pattern 3: Symbol anywhere in filename
        pattern3 = r'([A-Z]{6,10})'  # 6-10 uppercase letters (typical for forex/index symbols)
        match3 = re.search(pattern3, base_name)
        if match3:
            return match3.group(1)

        # Fallback: use first part of filename
        parts = base_name.split('_')
        if parts:
            # Clean up the first part to extract symbol-like text
            clean_part = re.sub(r'[^A-Z]', '', parts[0].upper())
            if len(clean_part) >= 3:
                return clean_part[:10]  # Limit to 10 chars

        return "UNKNOWN"

    def _get_next_gipsy_danger_number(self):
        """Get the next sequential number for Gipsy Danger EA"""
        counter_file = os.path.join(config.RESULTS_DIR, '.gipsy_danger_counter')

        # Create results directory if it doesn't exist
        os.makedirs(config.RESULTS_DIR, exist_ok=True)

        # Read current counter or start at 1
        if os.path.exists(counter_file):
            try:
                with open(counter_file, 'r') as f:
                    current_number = int(f.read().strip())
            except (ValueError, FileNotFoundError):
                current_number = 0
        else:
            current_number = 0

        # Increment and save
        next_number = current_number + 1
        with open(counter_file, 'w') as f:
            f.write(str(next_number))

        return f"{next_number:03d}"  # Format as 001, 002, etc.
        
    def discover_patterns(self, data_file):
        """Main function to discover patterns using LLM - COMPLETELY AUTONOMOUS"""
        import os  # Import os at the beginning of the method

        print("🧠 CORTEX - Autonomous AI Pattern Discovery Starting...")
        print("   No user input required - Cortex will discover patterns automatically")

        # FAIL HARD: Validate input file first (before any other checks)
        if not data_file:
            raise RuntimeError("FAIL HARD: No data file provided")

        if not os.path.exists(data_file):
            raise RuntimeError(f"FAIL HARD: Data file does not exist: {data_file}")

        if not data_file.lower().endswith(('.csv', '.xlsx', '.xls')):
            raise RuntimeError(f"FAIL HARD: Unsupported file format: {data_file}")

        # Extract symbol from filename
        filename = os.path.basename(data_file)
        symbol = self._extract_symbol_from_filename(filename)
        print(f"📊 Detected Symbol: {symbol}")

        # Check LLM availability
        if not self.ai_client.is_server_running():
            error_msg = "❌ LM Studio not running. Please start LM Studio with a loaded model."
            logger.error(error_msg)
            print(error_msg)
            return None

        # ARCHITECTURAL FIX: Use new data ingestion architecture
        # CSV → backtesting.py (data ingestion) → behavioral_intelligence.py (behavioral intelligence) → Cortex (LLM analysis)
        print("📊 Loading market data using backtesting.py native capabilities...")
        from data_ingestion import DataIngestionManager
        from backtesting import Backtest, Strategy
        import numpy as np
        import pandas as pd
        from llm_rule_parser import parse_llm_rule, generate_mt4_ea, RuleParseError, extract_individual_patterns
        from behavioral_intelligence import generate_clean_timeframes

        # CLEAN: Load data using DataIngestionManager directly (no wrapper functions)
        try:
            ingestion_manager = DataIngestionManager()
            raw_data = ingestion_manager.load_market_data(data_file)
            ohlc_data = ingestion_manager.prepare_for_backtesting(raw_data)
            print(f"✅ Data loaded: {len(ohlc_data)} records from {ohlc_data.index.min()} to {ohlc_data.index.max()}")
        except Exception as e:
            print(f"❌ Data loading failed: {e}")
            return None

        # Add hour column for time filtering (needed for LLM analysis)
        ohlc_data['hour'] = ohlc_data.index.hour

        # Create full_data for compatibility with existing code
        full_data = ohlc_data.reset_index()
        full_data.rename(columns={'DateTime': 'datetime'}, inplace=True)

        # Generate timeframes with behavioral intelligence using behavioral_intelligence.py
        print("📊 Preparing multi-timeframe data with behavioral intelligence...")
        from behavioral_intelligence import generate_clean_timeframes
        timeframe_data = generate_clean_timeframes(ohlc_data)

        # Load previous feedback for LLM learning loop (with investigation mode)
        disable_learning = os.environ.get('JAEGER_DISABLE_LEARNING', 'false').lower() == 'true'

        if disable_learning:
            print(f"🔬 INVESTIGATION: Learning data DISABLED for testing")
            previous_feedback = []
        else:
            print(f"🔍 Checking for previous LLM learning data in /llm_data/{symbol}/...")
            previous_feedback = self._load_previous_feedback(symbol)
            if previous_feedback:
                print(f"✅ Found {len(previous_feedback)} previous LLM sessions for {symbol}")
            else:
                print(f"📝 No previous LLM learning data found for {symbol} - starting fresh")

        # LLM discovers patterns autonomously (with full OHLC freedom and previous feedback)
        analysis = self._autonomous_llm_analysis(ohlc_data, full_data, previous_feedback, timeframe_data)
        if analysis is None:
            return None

        # Extract the executable rule text from the LLM analysis
        llm_rule_text = analysis if isinstance(analysis, str) else str(analysis)

        # DEBUG: Show what the LLM actually generated
        print("🔍 DEBUG - LLM Generated Pattern Text:")
        print("=" * 60)
        print(llm_rule_text[:1000] + "..." if len(llm_rule_text) > 1000 else llm_rule_text)
        print("=" * 60)

        # Parse rules using the new parser with timeframe data AND extract individual patterns
        try:
            rule_functions = parse_llm_rule(llm_rule_text, timeframe_data)
            individual_patterns = extract_individual_patterns(llm_rule_text)

            if not rule_functions:
                print("❌ Cortex rule parsing failed: No valid rules found")
                print("🔍 LLM Response Preview (first 500 chars):")
                print(llm_rule_text[:500] + "..." if len(llm_rule_text) > 500 else llm_rule_text)
                print("🔍 Looking for sections containing 'Entry condition', 'Direction', 'Stop loss', 'Exit condition'")
                return None

            if len(rule_functions) != len(individual_patterns):
                print(f"⚠️  Warning: {len(rule_functions)} rules but {len(individual_patterns)} patterns")
                print("   Using individual pattern extraction for proper testing")

            print(f"✅ Successfully parsed {len(rule_functions)} trading rules")
            print(f"✅ Extracted {len(individual_patterns)} individual patterns for separate testing")

            # Generate MT4 EA code with Gipsy Danger naming
            gipsy_number = self._get_next_gipsy_danger_number()
            ea_name = f"Gipsy_Danger_{gipsy_number}"
            mt4_ea_code = generate_mt4_ea(llm_rule_text, ea_name)
            
        except RuleParseError as e:
            print(f"❌ Cortex rule parsing failed: {e}")
            return None
        except Exception as e:
            print(f"❌ Unexpected error during rule parsing: {e}")
            return None

        # CORTEX ORCHESTRATES: LLM → BACKTESTING → FILE GENERATION
        print(f"\n🔄 CORTEX ORCHESTRATING BACKTESTING...")

        # Call backtesting module to test the LLM patterns
        backtest_results = self._orchestrate_backtesting(
            rule_functions, individual_patterns, ohlc_data, timeframe_data
        )

        # Save LLM feedback with backtest results
        self._save_llm_feedback(symbol, analysis)

        print(f"🔄 CORTEX ORCHESTRATING FILE GENERATION...")

        # Call file generator to create all output files
        cortex_results = {
            'llm_analysis': analysis,
            'rule_functions': rule_functions,
            'individual_patterns': individual_patterns,
            'mt4_ea_code': mt4_ea_code,
            'ea_name': ea_name,
            'symbol': symbol,
            'ohlc_data': ohlc_data,
            'full_data': full_data,
            'timeframe_data': timeframe_data
        }

        generated_files = self._orchestrate_file_generation(cortex_results, backtest_results)

        print(f"✅ CORTEX ORCHESTRATION COMPLETE")
        print(f"   🧠 LLM analysis: ✅")
        print(f"   📊 Backtesting: ✅")
        print(f"   📁 File generation: ✅")

        # Return complete trading system (like before)
        return {
            'system_file': generated_files.get('trading_system_report'),
            'ea_file': generated_files.get('mt4_ea_file'),
            'html_files': generated_files.get('html_charts', []),
            'csv_files': generated_files.get('csv_files', []),
            'llm_analysis': analysis,
            'backtest_results': backtest_results,
            'performance': {
                'total_records': len(ohlc_data),
                'time_range': f"{ohlc_data.index.min()} to {ohlc_data.index.max()}",
                'patterns_tested': len(rule_functions),
                'patterns_profitable': len([r for r in backtest_results if r.get('is_profitable', False)])
            }
        }



    def _autonomous_llm_analysis(self, ohlc_data, full_data, previous_feedback=None, timeframe_data=None):
        """Cortex coordinates data preparation and sends everything to LLM for SITUATIONAL PATTERN discovery"""
        print("🧠 CORTEX coordinating LLM pattern discovery process...")
        print("📊 Preparing market data and behavioral context for LLM analysis...")

        # ARCHITECTURAL FIX: Use behavioral_intelligence.py for behavioral analysis
        # This follows the documented architecture: backtesting.py → behavioral_intelligence.py → Cortex
        print("📊 Calculating fresh behavioral metrics from current market data...")
        from behavioral_intelligence import generate_behavioral_summaries

        # Use the timeframe data passed from discover_patterns (no duplicate generation)
        summaries_str = generate_behavioral_summaries(timeframe_data)

        # NEW: Generate performance feedback context for LLM learning loop
        feedback_context = self._generate_performance_feedback_context(previous_feedback)
        if feedback_context:
            print("🔄 Including previous LLM learning insights in pattern discovery prompt...")
            print(f"📊 Learning context size: {len(feedback_context)} characters")
        else:
            print("📝 No learning context to include - LLM will discover patterns without historical insights")

        # Prepare comprehensive prompt with all data for LLM pattern discovery
        print("📝 Preparing comprehensive prompt with market data + behavioral metrics + learning history...")
        prompt = SituationalAnalysisPrompts.generate_situational_discovery_prompt(
            ohlc_data=ohlc_data,  # Original data provides basic context (record count, price range)
            profit_context="",  # No pre-screening - let LLM discover patterns
            market_summaries=summaries_str,  # Rich multi-dimensional behavioral analysis of ALL timeframes
            performance_feedback=feedback_context  # Previous pattern performance insights from /llm_data/
        )

        # Send to LLM for actual pattern discovery
        print("🤖 Sending data to LLM for SITUATIONAL PATTERN discovery...")
        print(f"📏 Prompt size: {len(prompt)} characters (~{len(prompt)//4} tokens)")
        try:
            response = self.ai_client.send_message(prompt, temperature=config.LLM_TEMPERATURE, max_tokens=config.LLM_MAX_TOKENS)
            
            # Validate response for fabricated metrics using fact checker
            llm_response = response.get('response', '')
            fact_checker = LLMFactChecker(full_data)  # Use full data with hour column
            validation_result = fact_checker.validate_response(llm_response)

            print("✅ LLM pattern discovery complete - received trading patterns from AI")
            print("🔍 Fact-checking LLM response for data accuracy...")
            return validation_result

        except RuntimeError as e:
            print(f"❌ LLM Communication Error: {e}")
            return None

    def _generate_performance_feedback_context(self, previous_feedback):
        """Generate ENHANCED performance feedback context for LLM learning loop"""
        if not previous_feedback or not isinstance(previous_feedback, list):
            return ""

        feedback_lines = []
        feedback_lines.append("🧠 ENHANCED PATTERN LEARNING INTELLIGENCE:")
        feedback_lines.append("==========================================")

        # Process enhanced session data
        strategic_insights = []
        learning_recommendations = []
        validation_intelligence = []
        pattern_intelligence = []

        for session in previous_feedback[-3:]:  # Last 3 sessions for focused learning
            # Traditional feedback
            feedback = session.get('feedback', {})
            if isinstance(feedback, dict):
                performance_summary = feedback.get('performance_summary', 'No summary available')
                key_insights = feedback.get('key_insights', [])

                if performance_summary != 'No summary available':
                    feedback_lines.append(f"\n📊 Recent Pattern: {performance_summary}")
                    if key_insights:
                        for insight in key_insights[:2]:  # Top 2 insights per pattern
                            feedback_lines.append(f"  • {insight}")

            # ENHANCED learning intelligence
            learning_intel = session.get('learning_intelligence', {})
            strategic_insights.extend(learning_intel.get('strategic_insights', []))
            learning_recommendations.extend(learning_intel.get('learning_recommendations', []))

            # Validation intelligence
            validation_metrics = session.get('validation_metrics', {})
            if validation_metrics.get('avg_validation_score', 0) > 0:
                score = validation_metrics['avg_validation_score']
                quality = validation_metrics.get('quality_distribution', {})
                validation_intelligence.append(f"Validation score: {score:.3f}, Quality: {quality}")

            # Pattern characteristics intelligence
            pattern_chars = session.get('pattern_characteristics', {})
            if pattern_chars.get('dominant_execution_speed') and pattern_chars.get('dominant_execution_speed') != 'unknown':
                speed = pattern_chars['dominant_execution_speed']
                risk = pattern_chars.get('dominant_risk_profile', 'unknown')
                pattern_intelligence.append(f"Dominant style: {speed} execution, {risk} risk profile")

        # Add strategic intelligence section
        if strategic_insights:
            feedback_lines.append(f"\n🎯 STRATEGIC INTELLIGENCE:")
            for insight in strategic_insights[-3:]:  # Last 3 strategic insights
                feedback_lines.append(f"  • {insight}")

        # Add learning recommendations section
        if learning_recommendations:
            feedback_lines.append(f"\n💡 LEARNING RECOMMENDATIONS:")
            for rec in learning_recommendations[-3:]:  # Last 3 learning recommendations
                feedback_lines.append(f"  • {rec}")

        # Add validation intelligence
        if validation_intelligence:
            feedback_lines.append(f"\n📊 VALIDATION INTELLIGENCE:")
            for val_info in validation_intelligence[-2:]:  # Last 2 validation insights
                feedback_lines.append(f"  • {val_info}")

        # Add pattern intelligence
        if pattern_intelligence:
            feedback_lines.append(f"\n🎨 PATTERN INTELLIGENCE:")
            for pattern_info in pattern_intelligence[-2:]:  # Last 2 pattern insights
                feedback_lines.append(f"  • {pattern_info}")

        if len(feedback_lines) > 2:  # More than just headers
            feedback_lines.append(f"\n🚀 ENHANCED LEARNING DIRECTIVE:")
            feedback_lines.append("Use this multi-dimensional intelligence to discover superior situational patterns.")
            feedback_lines.append("Focus on validation quality, execution characteristics, and strategic insights.")
            return "\n".join(feedback_lines)

        return ""

    def _load_previous_feedback(self, symbol):
        """Load previous ENHANCED LLM feedback for learning loop - last 100 sessions"""
        # Store feedback in organized /llm_data/SYMBOL/ structure
        llm_data_dir = os.path.join(os.path.dirname(config.RESULTS_DIR), 'llm_data', symbol)

        if not os.path.exists(llm_data_dir):
            return []

        try:
            # Get all session files, sorted by timestamp (newest first)
            session_files = []
            for filename in os.listdir(llm_data_dir):
                if filename.startswith('session_') and filename.endswith('.json'):
                    filepath = os.path.join(llm_data_dir, filename)
                    session_files.append((filepath, os.path.getmtime(filepath)))

            # Sort by modification time (newest first) and take last 100
            session_files.sort(key=lambda x: x[1], reverse=True)
            recent_sessions = session_files[:100]

            # Load ENHANCED session data from recent sessions
            all_sessions = []
            for filepath, _ in recent_sessions:
                try:
                    with open(filepath, 'r') as f:
                        session_data = json.load(f)
                        # Load complete enhanced session data (not just feedback)
                        if session_data:
                            all_sessions.append(session_data)
                except Exception as e:
                    logger.warning(f"Failed to load session file {filepath}: {e}")
                    continue

            logger.info(f"Loaded enhanced data from {len(all_sessions)} previous sessions for {symbol}")
            return all_sessions

        except Exception as e:
            logger.warning(f"Failed to load previous enhanced feedback for {symbol}: {e}")
            return []

    def _orchestrate_backtesting(self, rule_functions, individual_patterns, ohlc_data, timeframe_data):
        """CORTEX ORCHESTRATES: Call backtesting module to test LLM patterns"""
        print("📊 Running backtests on LLM-generated patterns...")

        from backtesting import Backtest, Strategy
        import pandas as pd

        backtest_results = []

        for i, (rule_func, pattern_text) in enumerate(zip(rule_functions, individual_patterns), 1):
            print(f"   🔍 Testing Pattern {i}...")

            try:
                # Create Strategy class for this pattern
                class PatternStrategy(Strategy):
                    def init(self):
                        self.rule_functions = [rule_func]
                        # CRITICAL FIX: Store full dataset for rule evaluation
                        # The backtesting framework truncates self.data during next() calls
                        # but our rule functions need access to full historical data
                        self.full_ohlc_data = ohlc_data.copy()
                        # Store pattern text for risk analysis
                        self.pattern_text = pattern_text
                        # Initialize counters for diagnostics
                        self.signal_count = 0
                        self.order_count = 0
                        self._order_rejection_count = 0
                        self._validation_failure_count = 0
                        self.bars_processed = 0

                    def next(self):
                        # CRITICAL FIX: Calculate correct index in full dataset
                        # The backtesting framework calls next() for each bar sequentially
                        # but len(self.data.Close) gives us the current bar position in the backtest
                        # We need to map this to the correct index in the full dataset
                        backtest_bar = len(self.data.Close) - 1

                        # CRITICAL: The full dataset index should match the backtest progression
                        # Since we're using the same dataset for both, the index should be the same
                        current_idx = backtest_bar

                        if current_idx < 2:
                            return

                        # Count bars processed for diagnostics
                        self.bars_processed += 1

                        # CRITICAL FIX: Use stored full dataset instead of truncated self.data
                        # Rule functions expect full historical data to access previous bars
                        try:
                            # DEBUG: Add detailed logging to trace the issue
                            if current_idx % 100 == 0:  # Log every 100 bars for better visibility
                                print(f"      🔍 Testing bar {current_idx}/{len(self.full_ohlc_data)} (processed: {self.bars_processed})")

                            signal = rule_func(self.full_ohlc_data, current_idx)

                            # DEBUG: Log signal results
                            if signal:
                                print(f"      🎯 CORTEX SIGNAL {self.signal_count + 1} at bar {current_idx}: {signal}")
                                self.signal_count += 1

                                # Position size is already set correctly by rule parser
                                position_size = signal.get('position_size', 1.0)  # Use 1 absolute unit as fallback

                                # Validate stop loss and take profit values
                                sl_price = signal.get('stop_loss')
                                tp_price = signal.get('take_profit')
                                entry_price = signal.get('entry_price')

                                # CRITICAL FIX: Validate order parameters before placing
                                if self._validate_order_parameters(signal['direction'], entry_price, sl_price, tp_price, current_idx):
                                    try:
                                        # CRITICAL FIX: Place market orders without limit price, then set SL/TP
                                        print(f"       Placing MARKET order: direction={signal['direction']}, size={position_size}")
                                        print(f"       Will set SL/TP after: sl={sl_price:.1f}, tp={tp_price:.1f}")

                                        if signal['direction'] == 'long':
                                            # Place market order first (no limit price)
                                            order = self.buy(size=position_size)
                                            if order:
                                                # Set SL/TP after order is placed
                                                try:
                                                    order.sl = sl_price
                                                    order.tp = tp_price
                                                    print(f"      ✅ LONG market order placed + SL/TP set: size={position_size}")
                                                    if not hasattr(self, 'order_count'):
                                                        self.order_count = 0
                                                    self.order_count += 1
                                                except Exception as sl_tp_error:
                                                    print(f"      ⚠️ Order placed but SL/TP failed: {sl_tp_error}")
                                                    if not hasattr(self, 'order_count'):
                                                        self.order_count = 0
                                                    self.order_count += 1
                                            else:
                                                print(f"      ❌ LONG market order returned None: size={position_size}")
                                        elif signal['direction'] == 'short':
                                            # Place market order first (no limit price)
                                            order = self.sell(size=position_size)
                                            if order:
                                                # Set SL/TP after order is placed
                                                try:
                                                    order.sl = sl_price
                                                    order.tp = tp_price
                                                    print(f"      ✅ SHORT market order placed + SL/TP set: size={position_size}")
                                                    if not hasattr(self, 'order_count'):
                                                        self.order_count = 0
                                                    self.order_count += 1
                                                except Exception as sl_tp_error:
                                                    print(f"      ⚠️ Order placed but SL/TP failed: {sl_tp_error}")
                                                    if not hasattr(self, 'order_count'):
                                                        self.order_count = 0
                                                    self.order_count += 1
                                            else:
                                                print(f"      ❌ SHORT market order returned None: size={position_size}")
                                    except Exception as order_error:
                                        print(f"      ❌ ORDER REJECTED at bar {current_idx}: {order_error}")
                                        print(f"         Signal: {signal}")
                                        print(f"         Position size attempted: {position_size} (type: {type(position_size)})")
                                        if not hasattr(self, '_order_rejection_count'):
                                            self._order_rejection_count = 0
                                        self._order_rejection_count += 1
                                else:
                                    if not hasattr(self, '_validation_failure_count'):
                                        self._validation_failure_count = 0
                                    self._validation_failure_count += 1
                            elif current_idx < 10:  # Show first few non-signals for debugging
                                print(f"      🔍 No signal at bar {current_idx}")
                        except Exception as e:
                            # Debug: Show first few errors to understand issues
                            if not hasattr(self, '_error_count'):
                                self._error_count = 0
                            if self._error_count < 3:
                                print(f"      ⚠️ Rule evaluation error at bar {current_idx}: {e}")
                                self._error_count += 1



                    def _validate_order_parameters(self, direction, entry_price, sl_price, tp_price, bar_idx):
                        """CRITICAL FIX: Validate order parameters to prevent rejections"""
                        if not all([entry_price, sl_price, tp_price]):
                            print(f"      ❌ Missing order parameters at bar {bar_idx}: entry={entry_price}, sl={sl_price}, tp={tp_price}")
                            return False

                        # Validate LONG order relationships
                        if direction == 'long':
                            if sl_price >= entry_price:
                                print(f"      ❌ LONG order invalid at bar {bar_idx}: SL ({sl_price}) >= Entry ({entry_price})")
                                return False
                            if tp_price <= entry_price:
                                print(f"      ❌ LONG order invalid at bar {bar_idx}: TP ({tp_price}) <= Entry ({entry_price})")
                                return False
                            # Check minimum distance (spread consideration)
                            min_distance = 0.5  # Minimum 0.5 points
                            if (entry_price - sl_price) < min_distance:
                                print(f"      ❌ LONG order invalid at bar {bar_idx}: SL too close to entry (distance: {entry_price - sl_price})")
                                return False
                            if (tp_price - entry_price) < min_distance:
                                print(f"      ❌ LONG order invalid at bar {bar_idx}: TP too close to entry (distance: {tp_price - entry_price})")
                                return False

                        # Validate SHORT order relationships
                        elif direction == 'short':
                            if sl_price <= entry_price:
                                print(f"      ❌ SHORT order invalid at bar {bar_idx}: SL ({sl_price}) <= Entry ({entry_price})")
                                return False
                            if tp_price >= entry_price:
                                print(f"      ❌ SHORT order invalid at bar {bar_idx}: TP ({tp_price}) >= Entry ({entry_price})")
                                return False
                            # Check minimum distance
                            min_distance = 0.5  # Minimum 0.5 points
                            if (sl_price - entry_price) < min_distance:
                                print(f"      ❌ SHORT order invalid at bar {bar_idx}: SL too close to entry (distance: {sl_price - entry_price})")
                                return False
                            if (entry_price - tp_price) < min_distance:
                                print(f"      ❌ SHORT order invalid at bar {bar_idx}: TP too close to entry (distance: {entry_price - tp_price})")
                                return False

                        return True

                # Run backtest with realistic 1-pip spread
                bt = Backtest(
                    ohlc_data,
                    PatternStrategy,
                    cash=config.DEFAULT_INITIAL_CASH,
                    spread=config.DEFAULT_SPREAD,  # 1-pip realistic spread
                    commission=config.DEFAULT_COMMISSION,
                    margin=config.DEFAULT_MARGIN,
                    exclusive_orders=config.DEFAULT_EXCLUSIVE_ORDERS,
                    finalize_trades=config.DEFAULT_FINALIZE_TRADES
                )

                stats = bt.run()

                # Determine if profitable
                is_profitable = stats.get('Return [%]', 0) > 0
                trade_count = stats.get('# Trades', 0)

                print(f"      📊 Pattern {i}: {trade_count} trades, {stats.get('Return [%]', 0):.2f}% return")

                # CRITICAL FIX: Access strategy instance from stats, not bt._strategy
                # bt._strategy contains the strategy CLASS, stats._strategy contains the actual INSTANCE with counters
                strategy_instance = stats._strategy
                signals = getattr(strategy_instance, 'signal_count', 0)
                orders = getattr(strategy_instance, 'order_count', 0)
                rejections = getattr(strategy_instance, '_order_rejection_count', 0)
                validations = getattr(strategy_instance, '_validation_failure_count', 0)

                print(f"      🎯 Signals generated: {signals}")
                print(f"      📊 Orders attempted: {orders}")
                print(f"      ⚠️  Order rejections: {rejections}")
                print(f"      ⚠️  Validation failures: {validations}")
                print(f"      ✅ Trades executed: {trade_count}")

                # DIAGNOSTIC: Calculate conversion rates
                if signals > 0:
                    signal_to_order_rate = (orders / signals) * 100
                    print(f"      📈 Signal→Order rate: {signal_to_order_rate:.1f}% ({orders}/{signals})")

                    if orders > 0:
                        order_to_trade_rate = (trade_count / orders) * 100
                        print(f"      📈 Order→Trade rate: {order_to_trade_rate:.1f}% ({trade_count}/{orders})")

                    # CRITICAL ISSUE DETECTION
                    if trade_count == 0 and signals > 0:
                        print(f"      🚨 ZERO TRADES ISSUE DETECTED:")
                        if orders == 0:
                            print(f"         → Problem: Signals not converting to orders (validation/logic issue)")
                        elif orders > 0:
                            print(f"         → Problem: Orders not executing as trades (position sizing/margin issue)")
                            print(f"         → Check: Position sizes, margin requirements, order parameters")
                else:
                    print(f"      ⚠️  No signals generated - check pattern entry conditions")

                backtest_results.append({
                    'pattern_id': i,
                    'pattern_text': pattern_text,
                    'backtesting_py_stats': stats,
                    'is_profitable': is_profitable,
                    'trade_count': trade_count,
                    'return_pct': stats.get('Return [%]', 0)
                })

            except Exception as e:
                print(f"      ❌ Pattern {i} failed: {e}")
                backtest_results.append({
                    'pattern_id': i,
                    'pattern_text': pattern_text,
                    'backtesting_py_stats': None,
                    'is_profitable': False,
                    'trade_count': 0,
                    'return_pct': 0,
                    'error': str(e)
                })

        profitable_count = len([r for r in backtest_results if r.get('is_profitable', False)])
        print(f"   ✅ Backtesting complete: {profitable_count}/{len(backtest_results)} patterns profitable")

        return backtest_results

    def _orchestrate_file_generation(self, cortex_results, backtest_results):
        """CORTEX ORCHESTRATES: Call file generator to create all output files"""
        print("📁 Generating trading system files...")

        from file_generator import FileGenerator

        file_gen = FileGenerator()
        generated_files = file_gen.generate_trading_system_files(cortex_results, backtest_results)

        print(f"   ✅ Files generated in: {generated_files.get('system_folder', 'Unknown')}")

        return generated_files

    def _save_llm_feedback(self, symbol, llm_analysis):
        """Save LLM feedback for future learning - CORTEX ONLY SAVES LLM DATA"""
        # Store feedback in organized /llm_data/SYMBOL/ structure
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        llm_data_dir = os.path.join(project_root, 'llm_data', symbol)
        os.makedirs(llm_data_dir, exist_ok=True)

        # Create session file with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        session_file = os.path.join(llm_data_dir, f"session_{timestamp}.json")

        # Save LLM session data (CORTEX ONLY HANDLES LLM DATA)
        try:
            session_data = {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'session_id': timestamp,
                'llm_analysis': llm_analysis,
                'feedback': {'llm_response': llm_analysis}
            }

            with open(session_file, 'w') as f:
                json.dump(session_data, f, indent=2)

            # Clean up old sessions - keep only last 100
            self._cleanup_old_sessions(llm_data_dir, max_sessions=100)

            logger.info(f"Saved LLM session {timestamp} for {symbol}")
            print(f"🧠 Saved LLM learning data to /llm_data/{symbol}/ (keeping last 100 sessions)")

        except Exception as e:
            logger.warning(f"Failed to save LLM feedback: {e}")







    def _cleanup_old_sessions(self, llm_data_dir, max_sessions=100):
        """Clean up old session files, keeping only the most recent ones"""
        try:
            # Get all session files with timestamps
            session_files = []
            for filename in os.listdir(llm_data_dir):
                if filename.startswith('session_') and filename.endswith('.json'):
                    filepath = os.path.join(llm_data_dir, filename)
                    session_files.append((filepath, os.path.getmtime(filepath)))

            # Sort by modification time (newest first)
            session_files.sort(key=lambda x: x[1], reverse=True)

            # Remove old sessions beyond the limit
            if len(session_files) > max_sessions:
                old_sessions = session_files[max_sessions:]
                for filepath, _ in old_sessions:
                    try:
                        os.remove(filepath)
                        logger.debug(f"Removed old session file: {os.path.basename(filepath)}")
                    except Exception as e:
                        logger.warning(f"Failed to remove old session file {filepath}: {e}")

                print(f"🧹 Cleaned up {len(old_sessions)} old sessions (keeping last {max_sessions})")

        except Exception as e:
            logger.warning(f"Failed to cleanup old sessions: {e}")

    # ARCHITECTURAL FIX: Timeframe generation removed from Cortex
    # Timeframe generation is now handled by behavioral_intelligence.py using backtesting.py's native resampling
    # This follows the documented architecture: backtesting.py → behavioral_intelligence.py → Cortex

    # ARCHITECTURAL FIX: Behavioral analysis removed from Cortex
    # Behavioral analysis is now handled by behavioral_intelligence.py
    # This eliminates duplication and follows the documented architecture






def main():
    """Main function - COMPLETELY AUTONOMOUS SITUATIONAL ANALYSIS"""
    print("🧠 AUTONOMOUS SITUATIONAL ANALYSIS Pattern Discovery")
    print("=" * 60)
    print("🎯 NO USER INPUT REQUIRED - AI discovers SITUATIONAL patterns automatically")
    print("📊 METHODOLOGY: Situational Analysis - Participant Behavior under Market Contexts")
    print("❌ NOT: Chart patterns, technical indicators, or fundamental analysis")
    print("✅ FOCUS: Market situations, participant behavior, statistical behavioral edges")

    # Check for data files
    data_dir = config.DATA_DIR
    if not os.path.exists(data_dir):
        logger.error(f"❌ Data directory not found: {data_dir}")
        print(f"❌ Data directory not found: {data_dir}")
        return

    files = [f for f in os.listdir(data_dir) if f.endswith(('.csv', '.xlsx', '.xls'))]
    if not files:
        logger.error(f"❌ No data files found in '{data_dir}' directory")
        print(f"❌ No data files found in '{data_dir}' directory")
        return

    print(f"\n📁 Found {len(files)} data files - analyzing all automatically:")

    # Process ALL files autonomously and track results
    cortex = Cortex()
    successful_files = []
    failed_files = []

    for file in files:
        print(f"\n{'='*60}")
        print(f"🔍 ANALYZING: {file}")
        print(f"{'='*60}")

        selected_file = os.path.join(data_dir, file)
        result = cortex.discover_patterns(selected_file)

        if result:
            print(f"✅ SUCCESS: {file}")
            print(f"   📄 Trading System: {result['system_file']}")
            if result.get('ea_file'):
                print(f"   🤖 MT4 Expert Advisor: {result['ea_file']}")
            print(f"   📊 Market Records: {result['performance']['total_records']}")
            print(f"   📊 Patterns Tested: {result['performance']['patterns_tested']}")
            print(f"   📊 Patterns Profitable: {result['performance']['patterns_profitable']}")

            # Show preview of LLM analysis
            print(f"\n📖 SITUATIONAL ANALYSIS Discovery Preview:")
            print("-" * 50)
            preview = result['llm_analysis'][:300] + "..." if len(result['llm_analysis']) > 300 else result['llm_analysis']
            print(preview)

            successful_files.append(file)
        else:
            print(f"❌ FAILED: {file}")
            failed_files.append(file)

    # CORTEX ORCHESTRATION RESULTS
    print(f"\n{'='*70}")
    if successful_files:
        print("🎉 CORTEX ORCHESTRATION COMPLETE")
        print(f"{'='*70}")
        print(f"✅ SUCCESSFUL TRADING SYSTEMS: {len(successful_files)}/{len(files)}")
        for file in successful_files:
            print(f"   ✅ {file}")
        print("📁 Check 'results/' folder for complete trading systems")
        print("🧠 Cortex orchestrated: LLM → Backtesting → File Generation")
        print("✅ All components working with realistic 1-pip spread")
    else:
        print("❌ CORTEX ORCHESTRATION FAILED")
        print(f"{'='*70}")
        print(f"❌ NO SUCCESSFUL TRADING SYSTEMS: 0/{len(files)} files processed")
        print("🔍 FAILURE ANALYSIS:")
        for file in failed_files:
            print(f"   ❌ {file} - Trading system generation failed")
        print("\n💡 RECOMMENDATIONS:")
        print("   • Check LLM connectivity and configuration")
        print("   • Verify backtesting module functionality")
        print("   • Review file generation permissions")
        print("   • Check data quality and format")


if __name__ == "__main__":
    main()
