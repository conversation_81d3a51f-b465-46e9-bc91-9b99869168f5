#!/usr/bin/env python3
"""
Situational Analysis Prompts for LLM Pattern Discovery
Implements the SITUATIONAL ANALYSIS methodology for market pattern discovery
"""

class SituationalAnalysisPrompts:
    """
    Collection of situational analysis prompts based on the SITUATIONAL ANALYSIS methodology
    Focus on market participant behavior under specific situational contexts
    """
    
    @staticmethod
    def get_core_situational_questions():
        """Core situational analysis questions that guide pattern discovery"""
        return [
            "When this market situation occurs, how do participants typically behave?",
            "What situational contexts create predictable behavioral responses?", 
            "Under what market conditions do statistical edges emerge from participant behavior?",
            "Why do certain market situations create measurable behavioral patterns?",
            "How do different participants react to similar market situations?",
            "What contextual factors create measurable behavioral responses?",
            "When do market inefficiencies emerge from situational dynamics?",
            "Under what conditions do participants behave predictably?"
        ]
    
    @staticmethod
    def get_tom_hougaard_examples():
        """<PERSON><PERSON>'s situational analysis examples that exemplify the methodology"""
        return [
            "If Thursday is higher than Friday, then what does the following Monday look like?",
            "Is there evidence to support that what Monday starts, Wednesday continues?",
            "How often is a low or a high made in the first 30 minutes of the day?",
            "How often do gaps occur? Do they always fill, as the saying goes?",
            "How often does the market have trend days? How do you trade a trend day?",
            "Are there common denominators between strong trend days?",
            "Is there support for the comment that strong Fridays means strong Mondays?",
            "If Tuesday is lower than Monday, then what does that mean for Wednesday?",
            "When volatility regime changes, how do participants behave differently?",
            "Under what session conditions do behavioral opportunities emerge?",
            "What participant interactions create statistical edges during specific market contexts?",
            "When do institutional vs retail behaviors create exploitable patterns?"
        ]
    
    @staticmethod
    def get_situational_categories():
        """Different categories of situational analysis contexts"""
        return {
            "temporal_situations": {
                "description": "Time-based market situations",
                "contexts": [
                    "Session transition situations",
                    "Event-response situational contexts", 
                    "Information processing periods",
                    "Time-delayed reaction patterns"
                ]
            },
            "participant_behavioral": {
                "description": "Participant behavior-based situations",
                "contexts": [
                    "Breakout and breakdown situational dynamics",
                    "Mean reversion situational contexts",
                    "Large vs small participant behavior differences",
                    "Institutional vs retail behavioral patterns"
                ]
            },
            "volatility_based": {
                "description": "Volatility regime-based situations", 
                "contexts": [
                    "Volatility regime situational analysis",
                    "Risk-reward situational optimization",
                    "Volatility transition behaviors",
                    "Risk-taking behavior changes"
                ]
            },
            "market_structure": {
                "description": "Market structure-based situations",
                "contexts": [
                    "Participant interaction situational analysis",
                    "Cross-market situational relationships",
                    "Order flow imbalance responses",
                    "Liquidity provider behavior"
                ]
            },
            "risk_management": {
                "description": "Risk management situational contexts",
                "contexts": [
                    "Stop loss situational dynamics",
                    "Position sizing situational analysis",
                    "Breakeven management behaviors",
                    "Multiple attempt vs single attempt behaviors"
                ]
            }
        }
    
    @staticmethod
    def generate_situational_discovery_prompt(ohlc_data, profit_context="", market_summaries="", performance_feedback=""):
        """
        Generate the main situational analysis prompt for pattern discovery
        Focus on situational behavior analysis rather than chart patterns
        NEW: Includes performance feedback for LLM learning loop
        """
        
        core_questions = SituationalAnalysisPrompts.get_core_situational_questions()
        tom_examples = SituationalAnalysisPrompts.get_tom_hougaard_examples()
        
        prompt = f"""You are an expert MQL4/MT4 programmer discovering SIMPLE trading patterns that can be directly coded as Expert Advisors.

🎯 GENERATE PATTERNS AS MT4-READY CODE LOGIC:
Instead of complex descriptions, provide patterns as direct MT4 conditions that execute frequently.

💰 TRADING COSTS TO CONSIDER:
- Spread: 1 pip (0.0001) - This is the cost of entering/exiting trades
- Commission: None
- Your patterns MUST account for this 1-pip spread cost to remain profitable
- Ensure risk-reward ratios are sufficient to overcome the 1-pip spread

🚨 CRITICAL CONSTRAINT: Any pattern you discover MUST be directly codable as MT4 EA logic.

EXAMPLE PATTERN FORMAT:
Entry: Close[0] > High[1]
Stop: Low[1]
Target: Entry + (Entry - Low[1]) * 1.5
Optimal Times: [OPTIONAL] London session (higher win rate) - NOT a restriction, just optimization info

🚨 CRITICAL: Patterns execute 24/7. Time info is only for optimization, not restrictions.

ENHANCED MARKET INTELLIGENCE BRIEFING:
- Records: {len(ohlc_data)}
- Price Range: {ohlc_data.get('close', ohlc_data.get('Close', ohlc_data.iloc[:, -2])).min():.2f} to {ohlc_data.get('close', ohlc_data.get('Close', ohlc_data.iloc[:, -2])).max():.2f}

🎯 CRITICAL MARKET REGIME ANALYSIS:
{SituationalAnalysisPrompts._analyze_market_regime(ohlc_data)}

🚨 CRITICAL PROFITABILITY REQUIREMENTS:
YOUR PRIMARY GOAL: Discover PROFITABLE patterns that consistently make money.

MANDATORY PROFITABILITY CRITERIA (accounting for 1-pip spread cost):
- Target patterns with >60% win rate OR >2:1 risk-reward ratio
- Ensure take profit targets are at least 3-5 pips to overcome spread costs
- Prioritize patterns that show consistent positive returns over time
- REJECT patterns with <40% win rate AND <1.5:1 risk-reward ratio
- Focus on patterns that generate substantial profits, not just signals
- Account for 1-pip spread: small profit targets (<3 pips) will be unprofitable

🚨 CRITICAL MARKET REGIME COMPLIANCE:
The market is in STRONG UPTREND (+5.45% total return).
- ONLY generate LONG (OP_BUY) patterns
- DO NOT generate any SHORT (OP_SELL) patterns
- ALL patterns must align with the uptrend direction
- Counter-trend patterns will be REJECTED automatically

⚠️ CRITICAL: Ignore patterns that generate many trades but lose money overall.
Only discover patterns that have a clear statistical edge and profit potential.
⚠️ CRITICAL: Any SHORT patterns in an UPTREND market will be automatically rejected.

{profit_context}

{performance_feedback}

🚀 ENHANCED ANALYTICAL CAPABILITIES:
You now have access to sophisticated market analysis across 7 dimensions:

1. **MARKET REGIME CONTEXT** - Volatility states (low/medium/high) and trend regimes (uptrend/downtrend/sideways)
2. **MOMENTUM PERSISTENCE** - Continuation vs reversal patterns, acceleration/deceleration signals
3. **VOLUME-PRICE RELATIONSHIPS** - Volume confirmation patterns, divergence signals
4. **SESSION TRANSITION BEHAVIOR** - London/NY sessions, overlap periods, transition effects
5. **FAILURE PATTERN ANALYSIS** - What makes breakouts fail, small range failures, follow-through rates
6. **MULTI-TIMEFRAME ALIGNMENT** - When different timeframes agree/disagree on direction
7. **PRICE LEVEL CLUSTERING** - Significant price levels where action concentrates

🧠 MT4-READY PATTERN DISCOVERY:
Your task is to discover patterns as DIRECT MT4 CODE LOGIC:

1. **MT4 CONDITIONS**: Use only MT4 syntax: Close[0], High[1], Low[2], Open[3], etc.
2. **MT4 TIME FILTERS**: Use Hour() function: Hour() >= 9 && Hour() <= 16
3. **MT4 CALCULATIONS**: Direct price calculations using MT4 syntax
4. **EXECUTABLE LOGIC**: Every condition must be directly codable in MQL4

CORE SITUATIONAL QUESTIONS TO EXPLORE:
{chr(10).join([f'- "{q}"' for q in core_questions[:4]])}

SITUATIONAL ANALYSIS EXAMPLES (Tom Hougaard methodology):
{chr(10).join([f'- "{example}"' for example in tom_examples[:8]])}

🔍 ENHANCED BEHAVIORAL ANALYSIS:
{market_summaries}

🎯 REQUIRED OUTPUT FORMAT - PROFITABLE MT4-READY PATTERNS:

### PROFITABILITY REQUIREMENT
Only discover patterns that meet profitability criteria:
- Expected win rate >60% OR risk-reward ratio >2:1
- Clear statistical edge based on market regime analysis
- Alignment with dominant trend (STRONG UPTREND = focus on longs)

### CRITICAL PATTERN RESTRICTIONS FOR UPTREND MARKET:
- ALL patterns MUST use "MT4 Direction: OP_BUY" (LONG only)
- NO patterns with "MT4 Direction: OP_SELL" (NO SHORTS)
- Entry conditions must favor upward price movement
- Pattern names must reflect LONG bias (Buy, Long, Bullish)

### RULE CLARITY REQUIREMENT

Discover 3-5 PROFITABLE patterns as DIRECT MT4 CODE LOGIC:

**PATTERN 1: [Simple Name]**
Market Logic: [Brief explanation why this MT4 logic works]
MT4 Entry: Close[0] > High[1]
MT4 Direction: OP_BUY
MT4 Stop: Low[1]
MT4 Target: Close[0] + (Close[0] - Low[1]) * 2.0
MT4 Position Size: 1 unit
MT4 Timeframe: PERIOD_M5
Optimal Times: [OPTIONAL] All hours (or specify if pattern shows timing advantage)

**PATTERN 2: [Simple Name 2]**
Market Logic: [Brief explanation why this MT4 logic works]
MT4 Entry: Close[0] > Close[1] && Volume[0] > Volume[1]
MT4 Direction: OP_BUY
MT4 Stop: Low[1]
MT4 Target: Close[0] + (Close[0] - Low[1]) * 1.5
MT4 Position Size: 1 unit
MT4 Timeframe: PERIOD_M5

**PATTERN 3: [Another Multi-dimensional Pattern]**
[Continue same format for additional sophisticated patterns...]

CRITICAL REQUIREMENTS:
- Use SITUATIONAL ANALYSIS to discover WHY situations create edges
- Translate situational insights into BREAKOUT TRADING RULES for execution
- Explain the participant psychology behind each situational edge (WHY)
- Provide clear breakout entry/exit rules for implementation (HOW)
- Ensure breakout rules are based on the discovered situational insights

PATTERN DISCOVERY vs RULE EXECUTION:
- USE behavioral intelligence, regime analysis, session data for PATTERN DISCOVERY
- TRANSLATE sophisticated insights into SIMPLE MT4-compatible execution rules
- Behavioral intelligence helps you FIND patterns, but rules must be MT4-simple

EXECUTION RULE REQUIREMENTS - MT4 EA COMPATIBLE:
- ALL RULES MUST BE CODEABLE AS MT4 EXPERT ADVISOR
- Use only MT4-available data: High[0], Low[1], Close[2], Open[3], etc.
- Time filters: Only Hour() function (e.g., "Hour() >= 9 && Hour() <= 16")
- Position sizing: MUST specify absolute units (1 unit, 2 units, etc.) - NO percentages
- NO complex cross-timeframe analysis in execution rules
- NO "candle position since open" or session analysis in execution
- NO behavioral intelligence or regime detection in execution
- Entry conditions: Simple price comparisons (Close[0] > High[1])
- Stop loss: Previous High/Low or percentage-based only
- Exit: Time-based (bars) or percentage profit targets only
- Keep execution conditions simple enough for MT4 MQL4 coding"""

        return prompt

    @staticmethod
    def _analyze_market_regime(ohlc_data):
        """Analyze market regime for intelligent pattern discovery"""
        try:
            # Get price data
            close_col = ohlc_data.get('close', ohlc_data.get('Close', ohlc_data.iloc[:, -2]))

            # Calculate trend
            start_price = close_col.iloc[0]
            end_price = close_col.iloc[-1]
            total_return = ((end_price - start_price) / start_price) * 100

            # Calculate volatility
            returns = close_col.pct_change().dropna()
            volatility = returns.std() * 100

            # Determine regime and bias
            if total_return > 2:
                regime = "STRONG UPTREND"
                bias = "🎯 FOCUS ON LONG PATTERNS - Avoid shorts against strong trend"
                pattern_guidance = "Prioritize breakout patterns, momentum continuation, pullback entries"
            elif total_return > 0.5:
                regime = "UPTRENDING"
                bias = "📈 FAVOR LONG PATTERNS - Be selective with shorts"
                pattern_guidance = "Use trend-following patterns, avoid aggressive counter-trend trades"
            elif total_return < -2:
                regime = "STRONG DOWNTREND"
                bias = "🎯 FOCUS ON SHORT PATTERNS - Avoid longs against strong trend"
                pattern_guidance = "Prioritize breakdown patterns, momentum continuation, bounce shorts"
            elif total_return < -0.5:
                regime = "DOWNTRENDING"
                bias = "📉 FAVOR SHORT PATTERNS - Be selective with longs"
                pattern_guidance = "Use trend-following patterns, avoid aggressive counter-trend trades"
            else:
                regime = "RANGING"
                bias = "⚖️ BALANCED APPROACH - Use both directions with mean reversion focus"
                pattern_guidance = "Focus on range-bound patterns, support/resistance bounces"

            return f"""
📊 Market Regime: {regime} ({total_return:+.2f}% total return)
📊 Volatility Level: {volatility:.2f}% (daily price movement)
{bias}
🧠 Pattern Strategy: {pattern_guidance}

⚠️ CRITICAL: Align your pattern discovery with this regime analysis.
   Patterns that fight the dominant trend will likely be unprofitable."""

        except Exception as e:
            return f"Market regime analysis unavailable: {e}"

    @staticmethod
    def generate_situational_validation_prompt(discovered_patterns, market_data):
        """
        Generate prompt for validating discovered patterns using situational analysis principles
        """
        return f"""Validate these discovered patterns using SITUATIONAL ANALYSIS principles:

DISCOVERED PATTERNS:
{discovered_patterns}

VALIDATION CRITERIA:
1. Does each pattern focus on participant behavior rather than chart patterns?
2. Is there a clear situational context that creates the behavioral response?
3. Does the pattern explain WHY participants behave this way in this situation?
4. Are the entry conditions based on situational behavior, not technical signals?
5. Does the statistical edge come from behavioral consistency in similar situations?

MARKET DATA CONTEXT:
- Total Records: {len(market_data)}
- Analysis Period: {market_data['datetime'].min()} to {market_data['datetime'].max()}

Provide validation feedback and suggest improvements to align with situational analysis methodology."""
